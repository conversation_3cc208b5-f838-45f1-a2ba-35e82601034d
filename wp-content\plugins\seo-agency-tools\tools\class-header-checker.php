<?php
/**
 * HTTP Header Checker Tool
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

require_once SEO_AGENCY_TOOLS_PLUGIN_DIR . 'tools/class-base-tool.php';

class SEO_Tools_Header_Checker extends SEO_Tools_Base_Tool {
    
    /**
     * Initialize tool
     */
    protected function init() {
        $this->name = __('HTTP Header Checker', 'seo-agency-tools');
        $this->description = __('Analyze HTTP headers for SEO, security, and performance insights.', 'seo-agency-tools');
        $this->icon = 'dashicons-admin-settings';
        $this->category = 'technical';
    }
    
    /**
     * Process tool request
     */
    public function process($data) {
        // Check rate limit
        if (!$this->check_rate_limit()) {
            return array(
                'success' => false,
                'error' => __('Rate limit exceeded. Please try again later.', 'seo-agency-tools')
            );
        }
        
        $url = isset($data['url']) ? sanitize_url($data['url']) : '';
        
        if (empty($url)) {
            return array(
                'success' => false,
                'error' => __('URL is required.', 'seo-agency-tools')
            );
        }
        
        $url = $this->validate_url($url);
        if (!$url) {
            return array(
                'success' => false,
                'error' => __('Invalid URL format.', 'seo-agency-tools')
            );
        }
        
        // Analyze HTTP headers
        $results = $this->analyze_http_headers($url);
        
        // Save results
        $this->save_result($url, $results);
        
        return array(
            'success' => true,
            'data' => $results
        );
    }
    
    /**
     * Analyze HTTP headers
     */
    private function analyze_http_headers($url) {
        $results = array(
            'url' => $url,
            'timestamp' => current_time('mysql'),
            'status_code' => null,
            'headers' => array(),
            'security_headers' => array(),
            'seo_headers' => array(),
            'performance_headers' => array(),
            'issues' => array(),
            'recommendations' => array(),
            'score' => 0
        );
        
        // Make request to get headers
        $response = $this->make_request($url, array(
            'method' => 'HEAD',
            'timeout' => 15
        ));
        
        if (!$response['success']) {
            // Try GET request if HEAD fails
            $response = $this->make_request($url, array('timeout' => 15));
        }
        
        if (!$response['success']) {
            $results['error'] = $response['error'];
            return $results;
        }
        
        $results['status_code'] = $response['status_code'];
        $headers = $response['headers'];
        
        // Convert headers to array format
        $results['headers'] = $this->normalize_headers($headers);
        
        // Analyze different types of headers
        $results['security_headers'] = $this->analyze_security_headers($results['headers']);
        $results['seo_headers'] = $this->analyze_seo_headers($results['headers']);
        $results['performance_headers'] = $this->analyze_performance_headers($results['headers']);
        
        // Generate issues and recommendations
        $results = $this->generate_header_recommendations($results);
        
        // Calculate overall score
        $results['score'] = $this->calculate_header_score($results);
        
        return $results;
    }
    
    /**
     * Normalize headers to consistent format
     */
    private function normalize_headers($headers) {
        $normalized = array();
        
        if (is_array($headers)) {
            foreach ($headers as $key => $value) {
                $key = strtolower($key);
                if (is_array($value)) {
                    $normalized[$key] = implode(', ', $value);
                } else {
                    $normalized[$key] = $value;
                }
            }
        }
        
        return $normalized;
    }
    
    /**
     * Analyze security headers
     */
    private function analyze_security_headers($headers) {
        $security_headers = array(
            'strict-transport-security' => array(
                'present' => isset($headers['strict-transport-security']),
                'value' => isset($headers['strict-transport-security']) ? $headers['strict-transport-security'] : null,
                'importance' => 'high',
                'description' => __('Enforces HTTPS connections', 'seo-agency-tools')
            ),
            'content-security-policy' => array(
                'present' => isset($headers['content-security-policy']),
                'value' => isset($headers['content-security-policy']) ? $headers['content-security-policy'] : null,
                'importance' => 'high',
                'description' => __('Prevents XSS and injection attacks', 'seo-agency-tools')
            ),
            'x-frame-options' => array(
                'present' => isset($headers['x-frame-options']),
                'value' => isset($headers['x-frame-options']) ? $headers['x-frame-options'] : null,
                'importance' => 'medium',
                'description' => __('Prevents clickjacking attacks', 'seo-agency-tools')
            ),
            'x-content-type-options' => array(
                'present' => isset($headers['x-content-type-options']),
                'value' => isset($headers['x-content-type-options']) ? $headers['x-content-type-options'] : null,
                'importance' => 'medium',
                'description' => __('Prevents MIME type sniffing', 'seo-agency-tools')
            ),
            'referrer-policy' => array(
                'present' => isset($headers['referrer-policy']),
                'value' => isset($headers['referrer-policy']) ? $headers['referrer-policy'] : null,
                'importance' => 'low',
                'description' => __('Controls referrer information', 'seo-agency-tools')
            ),
            'permissions-policy' => array(
                'present' => isset($headers['permissions-policy']),
                'value' => isset($headers['permissions-policy']) ? $headers['permissions-policy'] : null,
                'importance' => 'low',
                'description' => __('Controls browser features', 'seo-agency-tools')
            )
        );
        
        return $security_headers;
    }
    
    /**
     * Analyze SEO-related headers
     */
    private function analyze_seo_headers($headers) {
        $seo_headers = array(
            'canonical' => array(
                'present' => isset($headers['link']) && strpos($headers['link'], 'rel="canonical"') !== false,
                'value' => $this->extract_canonical_from_link_header($headers),
                'importance' => 'high',
                'description' => __('Canonical URL for duplicate content', 'seo-agency-tools')
            ),
            'robots' => array(
                'present' => isset($headers['x-robots-tag']),
                'value' => isset($headers['x-robots-tag']) ? $headers['x-robots-tag'] : null,
                'importance' => 'high',
                'description' => __('Search engine crawling directives', 'seo-agency-tools')
            ),
            'content-language' => array(
                'present' => isset($headers['content-language']),
                'value' => isset($headers['content-language']) ? $headers['content-language'] : null,
                'importance' => 'medium',
                'description' => __('Content language specification', 'seo-agency-tools')
            ),
            'vary' => array(
                'present' => isset($headers['vary']),
                'value' => isset($headers['vary']) ? $headers['vary'] : null,
                'importance' => 'medium',
                'description' => __('Caching variation headers', 'seo-agency-tools')
            )
        );
        
        return $seo_headers;
    }
    
    /**
     * Analyze performance headers
     */
    private function analyze_performance_headers($headers) {
        $performance_headers = array(
            'cache-control' => array(
                'present' => isset($headers['cache-control']),
                'value' => isset($headers['cache-control']) ? $headers['cache-control'] : null,
                'importance' => 'high',
                'description' => __('Browser caching directives', 'seo-agency-tools')
            ),
            'expires' => array(
                'present' => isset($headers['expires']),
                'value' => isset($headers['expires']) ? $headers['expires'] : null,
                'importance' => 'medium',
                'description' => __('Resource expiration time', 'seo-agency-tools')
            ),
            'etag' => array(
                'present' => isset($headers['etag']),
                'value' => isset($headers['etag']) ? $headers['etag'] : null,
                'importance' => 'medium',
                'description' => __('Resource version identifier', 'seo-agency-tools')
            ),
            'last-modified' => array(
                'present' => isset($headers['last-modified']),
                'value' => isset($headers['last-modified']) ? $headers['last-modified'] : null,
                'importance' => 'medium',
                'description' => __('Last modification date', 'seo-agency-tools')
            ),
            'content-encoding' => array(
                'present' => isset($headers['content-encoding']),
                'value' => isset($headers['content-encoding']) ? $headers['content-encoding'] : null,
                'importance' => 'high',
                'description' => __('Content compression method', 'seo-agency-tools')
            ),
            'server-timing' => array(
                'present' => isset($headers['server-timing']),
                'value' => isset($headers['server-timing']) ? $headers['server-timing'] : null,
                'importance' => 'low',
                'description' => __('Server performance metrics', 'seo-agency-tools')
            )
        );
        
        return $performance_headers;
    }
    
    /**
     * Extract canonical URL from Link header
     */
    private function extract_canonical_from_link_header($headers) {
        if (!isset($headers['link'])) {
            return null;
        }
        
        $link_header = $headers['link'];
        if (preg_match('/<([^>]+)>;\s*rel="canonical"/', $link_header, $matches)) {
            return $matches[1];
        }
        
        return null;
    }
    
    /**
     * Generate recommendations based on header analysis
     */
    private function generate_header_recommendations($results) {
        $issues = array();
        $recommendations = array();
        
        // Security header recommendations
        foreach ($results['security_headers'] as $header => $info) {
            if (!$info['present']) {
                $priority = $info['importance'];
                $issues[] = array(
                    'type' => 'security',
                    'priority' => $priority,
                    'header' => $header,
                    'message' => sprintf(__('Missing %s header', 'seo-agency-tools'), $header)
                );
                
                $recommendations[] = array(
                    'type' => 'security',
                    'priority' => $priority,
                    'header' => $header,
                    'message' => sprintf(__('Add %s header: %s', 'seo-agency-tools'), $header, $info['description']),
                    'example' => $this->get_header_example($header)
                );
            }
        }
        
        // Performance header recommendations
        if (!$results['performance_headers']['cache-control']['present']) {
            $issues[] = array(
                'type' => 'performance',
                'priority' => 'high',
                'header' => 'cache-control',
                'message' => __('Missing Cache-Control header', 'seo-agency-tools')
            );
            
            $recommendations[] = array(
                'type' => 'performance',
                'priority' => 'high',
                'header' => 'cache-control',
                'message' => __('Add Cache-Control header to improve caching', 'seo-agency-tools'),
                'example' => 'Cache-Control: public, max-age=31536000'
            );
        }
        
        if (!$results['performance_headers']['content-encoding']['present']) {
            $issues[] = array(
                'type' => 'performance',
                'priority' => 'high',
                'header' => 'content-encoding',
                'message' => __('Content is not compressed', 'seo-agency-tools')
            );
            
            $recommendations[] = array(
                'type' => 'performance',
                'priority' => 'high',
                'header' => 'content-encoding',
                'message' => __('Enable gzip or brotli compression', 'seo-agency-tools'),
                'example' => 'Content-Encoding: gzip'
            );
        }
        
        $results['issues'] = $issues;
        $results['recommendations'] = $recommendations;
        
        return $results;
    }
    
    /**
     * Get example value for header
     */
    private function get_header_example($header) {
        $examples = array(
            'strict-transport-security' => 'max-age=31536000; includeSubDomains',
            'content-security-policy' => "default-src 'self'",
            'x-frame-options' => 'DENY',
            'x-content-type-options' => 'nosniff',
            'referrer-policy' => 'strict-origin-when-cross-origin',
            'permissions-policy' => 'geolocation=(), microphone=(), camera=()'
        );
        
        return isset($examples[$header]) ? $examples[$header] : '';
    }
    
    /**
     * Calculate header score
     */
    private function calculate_header_score($results) {
        $score = 0;
        $max_score = 100;
        
        // Security headers score (40 points)
        $security_score = 0;
        $security_total = 0;
        foreach ($results['security_headers'] as $header => $info) {
            $weight = $info['importance'] === 'high' ? 3 : ($info['importance'] === 'medium' ? 2 : 1);
            $security_total += $weight;
            if ($info['present']) {
                $security_score += $weight;
            }
        }
        $score += $security_total > 0 ? round(($security_score / $security_total) * 40) : 0;
        
        // Performance headers score (40 points)
        $performance_score = 0;
        $performance_total = 0;
        foreach ($results['performance_headers'] as $header => $info) {
            $weight = $info['importance'] === 'high' ? 3 : ($info['importance'] === 'medium' ? 2 : 1);
            $performance_total += $weight;
            if ($info['present']) {
                $performance_score += $weight;
            }
        }
        $score += $performance_total > 0 ? round(($performance_score / $performance_total) * 40) : 0;
        
        // SEO headers score (20 points)
        $seo_score = 0;
        $seo_total = 0;
        foreach ($results['seo_headers'] as $header => $info) {
            $weight = $info['importance'] === 'high' ? 3 : ($info['importance'] === 'medium' ? 2 : 1);
            $seo_total += $weight;
            if ($info['present']) {
                $seo_score += $weight;
            }
        }
        $score += $seo_total > 0 ? round(($seo_score / $seo_total) * 20) : 0;
        
        return min($score, $max_score);
    }
    
    /**
     * Render tool form
     */
    public function render_form($atts = array()) {
        $atts['placeholder'] = __('Enter URL to check HTTP headers...', 'seo-agency-tools');
        $atts['button_text'] = __('Check Headers', 'seo-agency-tools');
        
        return $this->render_basic_form($atts);
    }
}
