<?php
/**
 * Database management class for SEO Agency Tools
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SEO_Tools_Database {
    
    /**
     * Table names
     */
    private $tool_results_table;
    private $tool_settings_table;
    
    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        $this->tool_results_table = $wpdb->prefix . 'seo_tool_results';
        $this->tool_settings_table = $wpdb->prefix . 'seo_tool_settings';
    }
    
    /**
     * Create database tables
     */
    public function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Tool results table
        $sql_results = "CREATE TABLE {$this->tool_results_table} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            tool_name varchar(100) NOT NULL,
            url varchar(500) NOT NULL,
            result_data longtext NOT NULL,
            status varchar(50) NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY tool_name (tool_name),
            KEY url (url(191)),
            KEY status (status),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        // Tool settings table
        $sql_settings = "CREATE TABLE {$this->tool_settings_table} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            tool_name varchar(100) NOT NULL,
            setting_key varchar(100) NOT NULL,
            setting_value longtext NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY tool_setting (tool_name, setting_key),
            KEY tool_name (tool_name)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_results);
        dbDelta($sql_settings);
    }
    
    /**
     * Save tool result
     */
    public function save_result($tool_name, $url, $result_data, $status = 'completed') {
        global $wpdb;
        
        return $wpdb->insert(
            $this->tool_results_table,
            array(
                'tool_name' => $tool_name,
                'url' => $url,
                'result_data' => maybe_serialize($result_data),
                'status' => $status
            ),
            array('%s', '%s', '%s', '%s')
        );
    }
    
    /**
     * Get tool results
     */
    public function get_results($tool_name = '', $limit = 50, $offset = 0) {
        global $wpdb;
        
        $where = '';
        $params = array();
        
        if (!empty($tool_name)) {
            $where = 'WHERE tool_name = %s';
            $params[] = $tool_name;
        }
        
        $sql = "SELECT * FROM {$this->tool_results_table} {$where} 
                ORDER BY created_at DESC 
                LIMIT %d OFFSET %d";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $results = $wpdb->get_results($wpdb->prepare($sql, $params));
        
        // Unserialize result data
        foreach ($results as $result) {
            $result->result_data = maybe_unserialize($result->result_data);
        }
        
        return $results;
    }
    
    /**
     * Get single result
     */
    public function get_result($id) {
        global $wpdb;
        
        $result = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$this->tool_results_table} WHERE id = %d",
            $id
        ));
        
        if ($result) {
            $result->result_data = maybe_unserialize($result->result_data);
        }
        
        return $result;
    }
    
    /**
     * Delete result
     */
    public function delete_result($id) {
        global $wpdb;
        
        return $wpdb->delete(
            $this->tool_results_table,
            array('id' => $id),
            array('%d')
        );
    }
    
    /**
     * Save tool setting
     */
    public function save_setting($tool_name, $setting_key, $setting_value) {
        global $wpdb;
        
        return $wpdb->replace(
            $this->tool_settings_table,
            array(
                'tool_name' => $tool_name,
                'setting_key' => $setting_key,
                'setting_value' => maybe_serialize($setting_value)
            ),
            array('%s', '%s', '%s')
        );
    }
    
    /**
     * Get tool setting
     */
    public function get_setting($tool_name, $setting_key, $default = '') {
        global $wpdb;
        
        $value = $wpdb->get_var($wpdb->prepare(
            "SELECT setting_value FROM {$this->tool_settings_table} 
             WHERE tool_name = %s AND setting_key = %s",
            $tool_name,
            $setting_key
        ));
        
        if ($value !== null) {
            return maybe_unserialize($value);
        }
        
        return $default;
    }
    
    /**
     * Get all settings for a tool
     */
    public function get_tool_settings($tool_name) {
        global $wpdb;
        
        $results = $wpdb->get_results($wpdb->prepare(
            "SELECT setting_key, setting_value FROM {$this->tool_settings_table} 
             WHERE tool_name = %s",
            $tool_name
        ));
        
        $settings = array();
        foreach ($results as $result) {
            $settings[$result->setting_key] = maybe_unserialize($result->setting_value);
        }
        
        return $settings;
    }
    
    /**
     * Delete tool setting
     */
    public function delete_setting($tool_name, $setting_key) {
        global $wpdb;
        
        return $wpdb->delete(
            $this->tool_settings_table,
            array(
                'tool_name' => $tool_name,
                'setting_key' => $setting_key
            ),
            array('%s', '%s')
        );
    }
    
    /**
     * Clean old results
     */
    public function clean_old_results($days = 30) {
        global $wpdb;
        
        return $wpdb->query($wpdb->prepare(
            "DELETE FROM {$this->tool_results_table} 
             WHERE created_at < DATE_SUB(NOW(), INTERVAL %d DAY)",
            $days
        ));
    }
    
    /**
     * Get results count
     */
    public function get_results_count($tool_name = '') {
        global $wpdb;
        
        if (!empty($tool_name)) {
            return $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM {$this->tool_results_table} WHERE tool_name = %s",
                $tool_name
            ));
        }
        
        return $wpdb->get_var("SELECT COUNT(*) FROM {$this->tool_results_table}");
    }
    
    /**
     * Get statistics
     */
    public function get_statistics() {
        global $wpdb;
        
        $stats = array();
        
        // Total results
        $stats['total_results'] = $wpdb->get_var("SELECT COUNT(*) FROM {$this->tool_results_table}");
        
        // Results by tool
        $tool_stats = $wpdb->get_results(
            "SELECT tool_name, COUNT(*) as count 
             FROM {$this->tool_results_table} 
             GROUP BY tool_name 
             ORDER BY count DESC"
        );
        
        $stats['by_tool'] = array();
        foreach ($tool_stats as $stat) {
            $stats['by_tool'][$stat->tool_name] = $stat->count;
        }
        
        // Results by status
        $status_stats = $wpdb->get_results(
            "SELECT status, COUNT(*) as count 
             FROM {$this->tool_results_table} 
             GROUP BY status"
        );
        
        $stats['by_status'] = array();
        foreach ($status_stats as $stat) {
            $stats['by_status'][$stat->status] = $stat->count;
        }
        
        // Recent activity (last 7 days)
        $stats['recent_activity'] = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$this->tool_results_table} 
             WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)"
        );
        
        return $stats;
    }
    
    /**
     * Drop tables (for uninstall)
     */
    public function drop_tables() {
        global $wpdb;
        
        $wpdb->query("DROP TABLE IF EXISTS {$this->tool_results_table}");
        $wpdb->query("DROP TABLE IF EXISTS {$this->tool_settings_table}");
    }
}
