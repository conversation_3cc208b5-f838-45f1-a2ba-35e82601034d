/* nav-menu */

/* @todo: determine if this is truly for nav menus only */
.no-js #message {
	display: block;
}

ul.add-menu-item-tabs li {
	padding: 3px 5px 4px 8px;
}

.accordion-section ul.category-tabs,
.accordion-section ul.add-menu-item-tabs,
.accordion-section ul.wp-tab-bar {
	margin: 0;
}

.accordion-section .categorychecklist {
	margin: 13px 0;
}

#nav-menu-meta .accordion-section-content {
	padding: 18px 13px;
	resize: vertical;
}

#nav-menu-meta .button-controls {
	margin-bottom: 0;
}

.has-no-menu-item .button-controls {
	display: none;
}

#nav-menus-frame {
	margin-left: 300px;
	margin-top: 23px;
}

#wpbody-content #menu-settings-column {
	display: inline;
	width: 281px;
	margin-left: -300px;
	clear: both;
	float: left;
	padding-top: 0;
}

#menu-settings-column .inside {
	clear: both;
	margin: 10px 0 0;
	height: 100%;
	max-height: inherit;
}

#menu-settings-column .categorydiv,
#menu-settings-column .customlinkdiv,
#menu-settings-column .posttypediv,
#menu-settings-column .taxonomydiv {
	max-height: inherit;
	height: 100%;
}

#menu-settings-column .wp-tab-panel,
#menu-settings-column .categorydiv div.tabs-panel,
#menu-settings-column .customlinkdiv div.tabs-panel,
#menu-settings-column .posttypediv div.tabs-panel,
#menu-settings-column .taxonomydiv div.tabs-panel {
	/* Allow space for content after tab panels in nav menu editor. */
	max-height: calc( 100% - 75px );
	height: 100%;
}

.metabox-holder-disabled .postbox,
.metabox-holder-disabled .accordion-section-content,
.metabox-holder-disabled .accordion-section-title {
	opacity: 0.5;
	filter: alpha(opacity=50);
}

.metabox-holder-disabled .button-controls .select-all {
	display: none;
}

#wpbody {
	position: relative;
}

.is-submenu {
	color: #50575e; /* #fafafa background */
	font-style: italic;
	font-weight: 400;
	margin-left: 4px;
}

.manage-menus {
	margin-top: 23px;
	padding: 10px;
	overflow: hidden;
	background: #fff;
}

.manage-menus .selected-menu,
.manage-menus select,
.manage-menus .submit-btn,
.nav-menus-php .add-new-menu-action {
	display: inline-block;
	margin-right: 3px;
	vertical-align: middle;
}

.manage-menus select,
.menu-location-menus select {
	max-width: 100%;
}

.menu-edit #post-body-content h3 {
	margin: 1em 0 10px;
}

#nav-menu-bulk-actions-top {
	margin: 1em 0;
}

#nav-menu-bulk-actions-bottom {
	margin: 1em 0;
	margin: calc( 1em + 9px ) 0;
}

.bulk-actions input.button {
	margin-right: 12px;
}

.bulk-select-button {
	position: relative;
	display: inline-block;
	padding: 0 10px;
	font-size: 13px;
	line-height: 2.15384615;
	height: auto;
	min-height: 30px;
	background: #f6f7f7;
	vertical-align: top;
	border: 1px solid #dcdcde;
	margin: 0;
	cursor: pointer;
	border-radius: 3px;
	white-space: nowrap;
	box-sizing: border-box;
}

.bulk-selection .bulk-select-button {
	color: #2271b1;
	border-color: #2271b1;
	background: #f6f7f7;
	vertical-align: top;
}

#pending-menu-items-to-delete {
	display: none;
}

.bulk-selection #pending-menu-items-to-delete {
	display: block;
	margin-top: 1em;
}

#pending-menu-items-to-delete p {
	margin-bottom: 0;
}

#pending-menu-items-to-delete ul {
	margin-top: 0;
	list-style: none;
}

#pending-menu-items-to-delete ul li {
	display: inline;
}

input.bulk-select-switcher + .bulk-select-button-label {
	vertical-align: inherit;
}

label.bulk-select-button:hover,
label.bulk-select-button:active,
label.bulk-select-button:focus-within {
	background: #f0f0f1;
	border-color: #0a4b78;
	color: #0a4b78;
}

input.bulk-select-switcher:focus + .bulk-select-button-label {
	color: #0a4b78;
}

.bulk-actions input.menu-items-delete {
	appearance: none;
	font-size: inherit;
	border: 0;
	line-height: 2.1em;
	background: none;
	cursor: pointer;
	text-decoration: underline;
	color: #b32d2e;
}

.bulk-actions input.menu-items-delete:hover {
	color: #b32d2e;
	border: none;
}

.bulk-actions input.menu-items-delete.disabled {
	display: none;
}

.menu-settings {
	border-top: 1px solid #f0f0f1;
	margin-top: 2em;
}

.menu-settings-group {
	margin: 0 0 10px;
	overflow: hidden;
	padding-left: 20%;
}

.menu-settings-group:last-of-type {
	margin-bottom: 0;
}

.menu-settings-input {
	float: left;
	margin: 0;
	width: 100%;
}

.menu-settings-group-name {
	float: left;
	clear: both;
	width: 25%;
	padding: 3px 0 0;
	margin-left: -25%; /* 20 container left padding x ( 100 container % width / 80 this % width ) */
}

.menu-settings label {
	vertical-align: baseline;
}

.menu-edit .checkbox-input {
	margin-top: 4px;
}

.theme-location-set {
	color: #646970;
	font-size: 11px;
}

/* Menu Container */

/* @todo: responsive view. */
#menu-management-liquid {
	float: left;
	min-width: 100%;
	margin-top: 3px;
}

/* @todo: responsive view. */
#menu-management {
	position: relative;
	margin-right: 20px;
	margin-top: -3px;
	width: 100%;
}

#menu-management .menu-edit {
	margin-bottom: 20px;
}

.nav-menus-php #post-body {
	padding: 0 10px;
	border-top: 1px solid #fff;
	border-bottom: 1px solid #dcdcde;
	background: #fff;
}

#nav-menu-header,
#nav-menu-footer {
	padding: 0 10px;
	background: #f6f7f7;
}

#nav-menu-header {
	border-bottom: 1px solid #dcdcde;
	margin-bottom: 0;
}

#nav-menu-header .menu-name-label {
	display: inline-block;
	vertical-align: middle;
	margin-right: 7px;
}

.nav-menus-php #post-body div.updated,
.nav-menus-php #post-body div.error {
	margin: 0;
}

.nav-menus-php #post-body-content {
	position: relative;
	float: none;
}

.nav-menus-php #post-body-content .post-body-plain {
	margin-bottom: 0;
}

#menu-management .menu-add-new abbr {
	font-weight: 600;
}

#select-nav-menu-container {
	text-align: right;
	padding: 0 10px 3px;
	margin-bottom: 5px;
}

#select-nav-menu {
	width: 100px;
	display: inline;
}

#menu-name-label {
	margin-top: -2px;
}

.widefat .menu-locations .menu-location-title {
	padding: 13px 10px 0;
}

.menu-location-title label {
	font-weight: 600;
}

.menu-location-menus select {
	float: left;
}

#locations-nav-menu-wrapper {
	padding: 5px 0;
}

.locations-nav-menu-select select {
	float: left;
	width: 160px;
	margin-right: 5px;
}

.locations-row-links {
	float: left;
	margin: 6px 0 0 6px;
}

.locations-edit-menu-link,
.locations-add-menu-link {
	margin: 0 3px;
}

.locations-edit-menu-link {
	padding-right: 3px;
	border-right: 1px solid #c3c4c7;
}

#menu-management .inside {
	padding: 0 10px;
}

/* Add Menu Item Boxes */
.postbox .howto input,
.customlinkdiv .menu-item-textbox {
	width: 180px;
	float: right;
}

.accordion-container .outer-border {
	margin: 0;
}

.customlinkdiv p {
	margin-top: 0
}

#nav-menu-theme-locations .howto select {
	width: 100%;
}

#nav-menu-theme-locations .button-controls {
	text-align: right;
}

.add-menu-item-view-all {
	height: 400px;
}

/* Button Primary Actions */
#menu-container .submit {
	margin: 0 0 10px;
	padding: 0;
}

/* @todo: is this actually used? */
#cancel-save {
	text-decoration: underline;
	font-size: 12px;
	margin-left: 20px;
	margin-top: 5px;
}

.button.right, .button-secondary.right, .button-primary.right {
	float: right;
}

/* Button Secondary Actions */
.list-controls {
	float: left;
	margin-top: 5px;
}

.add-to-menu {
	float: right;
}

.button-controls {
	clear: both;
	margin: 10px 0;
}

.show-all,
.hide-all {
	cursor: pointer;
}

.hide-all {
	display: none;
}

/* Create Menu */
#menu-name {
	width: 270px;
	vertical-align: middle;
}

#manage-menu .inside {
	padding: 0;
}

/* Custom Links */
#available-links dt {
	display: block;
}

#add-custom-link .howto {
	font-size: 12px;
}

#add-custom-link label span {
	display: block;
	float: left;
	margin-top: 5px;
	padding-right: 5px;
}

.menu-item-textbox {
	width: 180px;
}

.customlinkdiv label,
.nav-menus-php .howto span {
	float: left;
	margin-top: 6px;
}

/* Menu item types */
.quick-search {
	width: 190px;
}

.quick-search-wrap .spinner {
	float: none;
	margin: -3px -10px 0 0;
}

.nav-menus-php .list-wrap {
	display: none;
	clear: both;
	margin-bottom: 10px;
}

.nav-menus-php .postbox p.submit {
	margin-bottom: 0;
}

/* Listings */
.nav-menus-php .list li {
	display: none;
	margin: 0 0 5px;
}

.nav-menus-php .list li .menu-item-title {
	cursor: pointer;
	display: block;
}

.nav-menus-php .list li .menu-item-title input {
	margin-right: 3px;
	margin-top: -3px;
}

.menu-item-title input[type=checkbox] {
	display: inline-block;
	margin-top: -4px;
}

.menu-item-title .post-state {
	font-weight: 600;
}

/* Nav Menu */
#menu-container .inside {
	padding-bottom: 10px;
}

.menu {
	padding-top: 1em;
}

#menu-to-edit {
	margin: 0;
	padding: 0.1em 0;
}

.menu ul {
	width: 100%;
}

.menu li {
	margin-bottom: 0;
	position: relative;
}

.menu-item-bar {
	clear: both;
	line-height: 1.5;
	position: relative;
	margin: 9px 0 0;
}

.menu-item-bar .menu-item-handle {
	border: 1px solid #dcdcde;
	position: relative;
	padding: 10px 15px;
	height: auto;
	min-height: 20px;
	max-width: 382px;
	line-height: 2.30769230;
	overflow: hidden;
	word-wrap: break-word;
}

.menu-item-bar .menu-item-handle:hover {
	border-color: #8c8f94;
}

#menu-to-edit .menu-item-invalid .menu-item-handle {
	background: #fcf0f1;
	border-color: #d63638;
}

.no-js .menu-item-edit-active .item-edit {
	display: none;
}

.js .menu-item-handle {
	cursor: move;
}

.menu li.deleting .menu-item-handle {
	background-image: none;
	background-color: #f86368;
}

.menu-item-handle .item-title {
	font-size: 13px;
	font-weight: 600;
	line-height: 1.53846153;
	display: block;
	/* @todo: responsive view. */
	margin-right: 13em;
}

.menu-item-handle .menu-item-checkbox {
	display: none;
}

.bulk-selection .menu-item-handle .menu-item-checkbox {
	display: inline-block;
	margin-right: 6px;
}

.menu-item-handle .menu-item-title.no-title {
	color: #646970;
}

/* Sortables */
li.menu-item.ui-sortable-helper .menu-item-bar {
	margin-top: 0;
}

li.menu-item.ui-sortable-helper .menu-item-transport .menu-item-bar {
	margin-top: 9px; /* Must use the same value used by the dragged item .menu-item-bar */
}

.menu .sortable-placeholder {
	height: 35px;
	width: 410px;
	margin-top: 9px; /* Must use the same value used by the dragged item .menu-item-bar */
}

/* Hide the transport list when it's empty */
.menu-item .menu-item-transport:empty {
	display: none;
}

/* WARNING: The factor of 30px is hardcoded into the nav-menus JavaScript. */
.menu-item-depth-0 { margin-left: 0; }
.menu-item-depth-1 { margin-left: 30px; }
.menu-item-depth-2 { margin-left: 60px; }
.menu-item-depth-3 { margin-left: 90px; }
.menu-item-depth-4 { margin-left: 120px; }
.menu-item-depth-5 { margin-left: 150px; }
.menu-item-depth-6 { margin-left: 180px; }
.menu-item-depth-7 { margin-left: 210px; }
.menu-item-depth-8 { margin-left: 240px; }
.menu-item-depth-9 { margin-left: 270px; }
.menu-item-depth-10 { margin-left: 300px; }
.menu-item-depth-11 { margin-left: 330px; }

.menu-item-depth-0 .menu-item-transport { margin-left: 0; }
.menu-item-depth-1 .menu-item-transport { margin-left: -30px; }
.menu-item-depth-2 .menu-item-transport { margin-left: -60px; }
.menu-item-depth-3 .menu-item-transport { margin-left: -90px; }
.menu-item-depth-4 .menu-item-transport { margin-left: -120px; }
.menu-item-depth-5 .menu-item-transport { margin-left: -150px; }
.menu-item-depth-6 .menu-item-transport { margin-left: -180px; }
.menu-item-depth-7 .menu-item-transport { margin-left: -210px; }
.menu-item-depth-8 .menu-item-transport { margin-left: -240px; }
.menu-item-depth-9 .menu-item-transport { margin-left: -270px; }
.menu-item-depth-10 .menu-item-transport { margin-left: -300px; }
.menu-item-depth-11 .menu-item-transport { margin-left: -330px; }

body.menu-max-depth-0 { min-width: 950px !important; }
body.menu-max-depth-1 { min-width: 980px !important; }
body.menu-max-depth-2 { min-width: 1010px !important; }
body.menu-max-depth-3 { min-width: 1040px !important; }
body.menu-max-depth-4 { min-width: 1070px !important; }
body.menu-max-depth-5 { min-width: 1100px !important; }
body.menu-max-depth-6 { min-width: 1130px !important; }
body.menu-max-depth-7 { min-width: 1160px !important; }
body.menu-max-depth-8 { min-width: 1190px !important; }
body.menu-max-depth-9 { min-width: 1220px !important; }
body.menu-max-depth-10 { min-width: 1250px !important; }
body.menu-max-depth-11 { min-width: 1280px !important; }

/* Menu item controls */
.item-type {
	display: inline-block;
	padding: 12px 16px;
	color: #646970;
	font-size: 12px;
	line-height: 1.5;
}

.item-controls {
	font-size: 12px;
	position: absolute;
	right: 20px;
	top: -1px;
}

.item-controls a {
	text-decoration: none;
}

.item-controls a:hover {
	cursor: pointer;
}

.item-controls .item-order {
	padding-right: 10px;
}

.nav-menus-php .item-edit {
	position: absolute;
	right: -20px;
	top: 0;
	display: block;
	width: 30px;
	height: 40px;
	outline: none;
}

.no-js.nav-menus-php .item-edit {
	position: static;
	float: right;
	width: auto;
	height: auto;
	margin: 12px -10px 12px 0;
	padding: 0;
	color: #2271b1;
	text-decoration: underline;
	font-size: 12px;
	line-height: 1.5;
}

.no-js.nav-menus-php .item-edit .screen-reader-text {
	position: static;
	clip-path: none;
	width: auto;
	height: auto;
	margin: 0;
}

.nav-menus-php .item-edit:before {
	margin-top: 10px;
	margin-left: 4px;
	width: 20px;
	border-radius: 50%;
	text-indent: -1px; /* account for the dashicon alignment */
}

.no-js.nav-menus-php .item-edit:before {
	display: none;
}

.rtl .nav-menus-php .item-edit:before {
	text-indent: 1px; /* account for the dashicon alignment */
}

.js.nav-menus-php .item-edit:focus {
	box-shadow: none;
}

.nav-menus-php .item-edit:focus:before {
	box-shadow: 0 0 0 2px #2271b1;
	/* Only visible in Windows High Contrast mode */
	outline: 2px solid transparent;
}

/* Menu editing */
.menu-instructions-inactive {
	display: none;
}

.menu-item-settings {
	display: block;
	max-width: 392px;
	padding: 10px;
	position: relative;
	z-index: 10; /* Keep .item-title's shadow from appearing on top of .menu-item-settings */
	border: 1px solid #c3c4c7;
	border-top: none;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.menu-item-settings .field-move {
	margin: 3px 0 5px;
	line-height: 1.5;
}

.field-move-visual-label {
	float: left;
	margin-right: 4px;
}

.menu-item-settings .field-move .button-link {
	display: none;
	margin: 0 2px;
}

.menu-item-edit-active .menu-item-settings {
	display: block;
}

.menu-item-edit-inactive .menu-item-settings {
	display: none;
}

.add-menu-item-pagelinks {
	margin: .5em -10px;
	text-align: center;
}

.add-menu-item-pagelinks .page-numbers {
	display: inline-block;
	min-width: 20px;
}

.add-menu-item-pagelinks .page-numbers.dots {
	min-width: 0;
}

.link-to-original {
	display: block;
	margin: 0 0 15px;
	padding: 3px 5px 5px;
	border: 1px solid #dcdcde;
	color: #646970;
	font-size: 12px;
}

.link-to-original a {
	padding-left: 4px;
	font-style: normal;
}

.hidden-field {
	display: none;
}

.description-group {
	display: flex;
	column-gap: 10px;
}

.description-group > * {
	flex-grow: 1;
}

.menu-item-actions {
	padding-top: 15px;
	padding-bottom: 7px;
}

#cancel-save {
	cursor: pointer;
}

/* Major/minor publishing actions (classes) */
.nav-menus-php .major-publishing-actions {
	padding: 10px 0;
	display: flex;
	align-items: center;
}

.nav-menus-php .major-publishing-actions > * {
	margin-right: 10px;
}

.nav-menus-php .major-publishing-actions .form-invalid {
	padding-left: 4px;
	margin-left: -4px;
}

#nav-menus-frame,
.button-controls,
#menu-item-url-wrap,
#menu-item-name-wrap {
	display: block;
}

/* =Media Queries
-------------------------------------------------------------- */

@media only screen and (min-width: 769px) and (max-width: 1000px) {
	body.menu-max-depth-0 {
		min-width: 0 !important;
	}

	#menu-management-liquid {
		width: 100%;
	}

	.nav-menus-php #post-body-content {
		min-width: 0;
	}
}

@media screen and (max-width: 782px) {
	body.nav-menus-php,
	body.wp-customizer {
		min-width: 0 !important;
	}

	#nav-menus-frame {
		margin-left: 0;
		float: none;
		width: 100%;
	}

	#wpbody-content #menu-settings-column {
		display: block;
		width: 100%;
		float: none;
		margin-left: 0;
	}

	#side-sortables .add-menu-item-tabs {
		margin: 15px 0 14px;
	}

	ul.add-menu-item-tabs li.tabs {
		padding: 13px 15px 14px;
	}

	.nav-menus-php .customlinkdiv .howto input {
		width: 65%;
	}

	.nav-menus-php .quick-search {
		width: 85%;
	}

	#menu-management-liquid {
		margin-top: 25px;
	}

	.nav-menus-php .menu-name-label.howto span {
		margin-top: 13px
	}

	#menu-name {
		width: 100%;
	}

	.nav-menus-php #nav-menu-header .major-publishing-actions .publishing-action {
		padding-top: 1em;
	}

	.nav-menus-php .delete-action {
		font-size: 14px;
		line-height: 2.14285714;
	}

	.menu-item-bar .menu-item-handle,
	.menu-item-settings {
		width: auto;
	}

	.menu-item-settings {
		padding: 10px;
	}

	.menu-item-settings .description-group {
		display: block;
	}

	.menu-item-settings input {
		width: 100%;
	}

	.menu-item-settings input[type="checkbox"],
	.menu-item-settings input[type="radio"] {
		width: 25px;
	}

	.menu-settings-group {
		padding-left: 0;
		overflow: visible;
	}

	.menu-settings-group-name {
		float: none;
		width: auto;
		margin-left: 0;
		margin-bottom: 15px;
	}

	.menu-settings-input {
		float: none;
		margin-bottom: 15px;
	}

	.menu-edit .checkbox-input {
		margin-top: 0;
	}

	.manage-menus select {
		margin: 0.5em 0;
	}

	.wp-core-ui .manage-menus .button {
		margin-bottom: 0;
	}

	.widefat .menu-locations .menu-location-title {
		padding-top: 16px;
	}
}

@media only screen and (min-width: 783px) {
    @supports (position: sticky) and (scroll-margin-bottom: 130px) {

		#nav-menu-footer {
                position: sticky;
				bottom: 0;
				z-index: 10;
				box-shadow: 0 -1px 0 0 #ddd;
        }

        #save_menu_header {
                display: none;
        }
    }
}

@media only screen and (max-width: 768px) {
	/* menu locations */
	#menu-locations-wrap .widefat {
		width: 100%;
	}

	.bulk-select-button {
		padding: 5px 10px;
	}
}
