<?php
/**
 * Settings management class for SEO Agency Tools
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SEO_Tools_Settings {
    
    /**
     * Option name
     */
    private $option_name = 'seo_agency_tools_options';
    
    /**
     * Default settings
     */
    private $defaults = array();
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->set_defaults();
        add_action('admin_init', array($this, 'register_settings'));
    }
    
    /**
     * Set default settings
     */
    private function set_defaults() {
        $this->defaults = array(
            'enabled_tools' => array(
                'responsive_checker',
                'title_enhancer',
                'snippet_validator',
                'anchor_generator',
                'lazy_loading_tester',
                'bulk_meta_validator',
                'meta_validator',
                'sitemap_finder',
                'mx_lookup',
                'http2_checker',
                'cache_checker',
                'subdomain_finder',
                'ttfb_checker',
                'header_checker',
                'redirect_checker'
            ),
            'custom_css' => '',
            'custom_js' => '',
            'tool_settings' => array(),
            'shortcode_settings' => array(),
            'api_settings' => array(
                'rate_limit' => 100,
                'timeout' => 30,
                'user_agent' => 'SEO Agency Tools/1.0'
            ),
            'display_settings' => array(
                'show_branding' => true,
                'theme' => 'default',
                'color_scheme' => 'blue'
            ),
            'security_settings' => array(
                'require_login' => false,
                'allowed_roles' => array('administrator', 'editor'),
                'enable_captcha' => false
            )
        );
    }
    
    /**
     * Register settings
     */
    public function register_settings() {
        register_setting(
            'seo_agency_tools_settings',
            $this->option_name,
            array($this, 'sanitize_settings')
        );
        
        // General settings section
        add_settings_section(
            'general_settings',
            __('General Settings', 'seo-agency-tools'),
            array($this, 'general_settings_callback'),
            'seo_agency_tools_settings'
        );
        
        // Tool management section
        add_settings_section(
            'tool_settings',
            __('Tool Management', 'seo-agency-tools'),
            array($this, 'tool_settings_callback'),
            'seo_agency_tools_settings'
        );
        
        // Customization section
        add_settings_section(
            'customization_settings',
            __('Customization', 'seo-agency-tools'),
            array($this, 'customization_settings_callback'),
            'seo_agency_tools_settings'
        );
        
        // API settings section
        add_settings_section(
            'api_settings',
            __('API Settings', 'seo-agency-tools'),
            array($this, 'api_settings_callback'),
            'seo_agency_tools_settings'
        );
        
        // Security settings section
        add_settings_section(
            'security_settings',
            __('Security Settings', 'seo-agency-tools'),
            array($this, 'security_settings_callback'),
            'seo_agency_tools_settings'
        );
        
        $this->add_settings_fields();
    }
    
    /**
     * Add settings fields
     */
    private function add_settings_fields() {
        // Enabled tools field
        add_settings_field(
            'enabled_tools',
            __('Enabled Tools', 'seo-agency-tools'),
            array($this, 'enabled_tools_callback'),
            'seo_agency_tools_settings',
            'tool_settings'
        );
        
        // Custom CSS field
        add_settings_field(
            'custom_css',
            __('Custom CSS', 'seo-agency-tools'),
            array($this, 'custom_css_callback'),
            'seo_agency_tools_settings',
            'customization_settings'
        );
        
        // Custom JS field
        add_settings_field(
            'custom_js',
            __('Custom JavaScript', 'seo-agency-tools'),
            array($this, 'custom_js_callback'),
            'seo_agency_tools_settings',
            'customization_settings'
        );
        
        // Theme field
        add_settings_field(
            'theme',
            __('Theme', 'seo-agency-tools'),
            array($this, 'theme_callback'),
            'seo_agency_tools_settings',
            'customization_settings'
        );
        
        // Color scheme field
        add_settings_field(
            'color_scheme',
            __('Color Scheme', 'seo-agency-tools'),
            array($this, 'color_scheme_callback'),
            'seo_agency_tools_settings',
            'customization_settings'
        );
        
        // Rate limit field
        add_settings_field(
            'rate_limit',
            __('Rate Limit (requests per hour)', 'seo-agency-tools'),
            array($this, 'rate_limit_callback'),
            'seo_agency_tools_settings',
            'api_settings'
        );
        
        // Timeout field
        add_settings_field(
            'timeout',
            __('Request Timeout (seconds)', 'seo-agency-tools'),
            array($this, 'timeout_callback'),
            'seo_agency_tools_settings',
            'api_settings'
        );
        
        // Require login field
        add_settings_field(
            'require_login',
            __('Require Login', 'seo-agency-tools'),
            array($this, 'require_login_callback'),
            'seo_agency_tools_settings',
            'security_settings'
        );
    }
    
    /**
     * Section callbacks
     */
    public function general_settings_callback() {
        echo '<p>' . __('Configure general plugin settings.', 'seo-agency-tools') . '</p>';
    }
    
    public function tool_settings_callback() {
        echo '<p>' . __('Enable or disable specific tools.', 'seo-agency-tools') . '</p>';
    }
    
    public function customization_settings_callback() {
        echo '<p>' . __('Customize the appearance and behavior of your tools.', 'seo-agency-tools') . '</p>';
    }
    
    public function api_settings_callback() {
        echo '<p>' . __('Configure API and performance settings.', 'seo-agency-tools') . '</p>';
    }
    
    public function security_settings_callback() {
        echo '<p>' . __('Configure security and access control settings.', 'seo-agency-tools') . '</p>';
    }
    
    /**
     * Field callbacks
     */
    public function enabled_tools_callback() {
        $options = $this->get_options();
        $enabled_tools = $options['enabled_tools'];
        
        $available_tools = array(
            'responsive_checker' => __('Responsive Design Checker', 'seo-agency-tools'),
            'title_enhancer' => __('Page Title Enhancer', 'seo-agency-tools'),
            'snippet_validator' => __('Rich Snippet Validator', 'seo-agency-tools'),
            'anchor_generator' => __('Anchor Text Generator', 'seo-agency-tools'),
            'lazy_loading_tester' => __('Lazy Loading Tester', 'seo-agency-tools'),
            'bulk_meta_validator' => __('Bulk Meta Tags Validator', 'seo-agency-tools'),
            'meta_validator' => __('Meta Tags Validator', 'seo-agency-tools'),
            'sitemap_finder' => __('Sitemap Finder', 'seo-agency-tools'),
            'mx_lookup' => __('MX Record Lookup', 'seo-agency-tools'),
            'http2_checker' => __('HTTP/2 Checker', 'seo-agency-tools'),
            'cache_checker' => __('Cache-Control Checker', 'seo-agency-tools'),
            'subdomain_finder' => __('Subdomain Finder', 'seo-agency-tools'),
            'ttfb_checker' => __('TTFB Checker', 'seo-agency-tools'),
            'header_checker' => __('HTTP Header Checker', 'seo-agency-tools'),
            'redirect_checker' => __('Redirect Checker', 'seo-agency-tools')
        );
        
        echo '<div class="tool-checkboxes">';
        foreach ($available_tools as $tool_key => $tool_name) {
            $checked = in_array($tool_key, $enabled_tools) ? 'checked' : '';
            echo '<label>';
            echo '<input type="checkbox" name="' . $this->option_name . '[enabled_tools][]" value="' . $tool_key . '" ' . $checked . '>';
            echo ' ' . $tool_name;
            echo '</label><br>';
        }
        echo '</div>';
    }
    
    public function custom_css_callback() {
        $options = $this->get_options();
        $custom_css = $options['custom_css'];
        
        echo '<textarea name="' . $this->option_name . '[custom_css]" rows="10" cols="80" class="large-text code">' . esc_textarea($custom_css) . '</textarea>';
        echo '<p class="description">' . __('Add custom CSS to style your tools.', 'seo-agency-tools') . '</p>';
    }
    
    public function custom_js_callback() {
        $options = $this->get_options();
        $custom_js = $options['custom_js'];
        
        echo '<textarea name="' . $this->option_name . '[custom_js]" rows="10" cols="80" class="large-text code">' . esc_textarea($custom_js) . '</textarea>';
        echo '<p class="description">' . __('Add custom JavaScript for enhanced functionality.', 'seo-agency-tools') . '</p>';
    }
    
    public function theme_callback() {
        $options = $this->get_options();
        $theme = $options['display_settings']['theme'];
        
        $themes = array(
            'default' => __('Default', 'seo-agency-tools'),
            'dark' => __('Dark', 'seo-agency-tools'),
            'light' => __('Light', 'seo-agency-tools'),
            'minimal' => __('Minimal', 'seo-agency-tools')
        );
        
        echo '<select name="' . $this->option_name . '[display_settings][theme]">';
        foreach ($themes as $value => $label) {
            $selected = selected($theme, $value, false);
            echo '<option value="' . $value . '" ' . $selected . '>' . $label . '</option>';
        }
        echo '</select>';
    }
    
    public function color_scheme_callback() {
        $options = $this->get_options();
        $color_scheme = $options['display_settings']['color_scheme'];
        
        $schemes = array(
            'blue' => __('Blue', 'seo-agency-tools'),
            'green' => __('Green', 'seo-agency-tools'),
            'red' => __('Red', 'seo-agency-tools'),
            'purple' => __('Purple', 'seo-agency-tools'),
            'orange' => __('Orange', 'seo-agency-tools')
        );
        
        echo '<select name="' . $this->option_name . '[display_settings][color_scheme]">';
        foreach ($schemes as $value => $label) {
            $selected = selected($color_scheme, $value, false);
            echo '<option value="' . $value . '" ' . $selected . '>' . $label . '</option>';
        }
        echo '</select>';
    }
    
    public function rate_limit_callback() {
        $options = $this->get_options();
        $rate_limit = $options['api_settings']['rate_limit'];
        
        echo '<input type="number" name="' . $this->option_name . '[api_settings][rate_limit]" value="' . $rate_limit . '" min="1" max="1000">';
        echo '<p class="description">' . __('Maximum number of requests per hour per IP address.', 'seo-agency-tools') . '</p>';
    }
    
    public function timeout_callback() {
        $options = $this->get_options();
        $timeout = $options['api_settings']['timeout'];
        
        echo '<input type="number" name="' . $this->option_name . '[api_settings][timeout]" value="' . $timeout . '" min="5" max="120">';
        echo '<p class="description">' . __('Request timeout in seconds.', 'seo-agency-tools') . '</p>';
    }
    
    public function require_login_callback() {
        $options = $this->get_options();
        $require_login = $options['security_settings']['require_login'];
        
        echo '<input type="checkbox" name="' . $this->option_name . '[security_settings][require_login]" value="1" ' . checked($require_login, 1, false) . '>';
        echo '<label>' . __('Require users to be logged in to use tools', 'seo-agency-tools') . '</label>';
    }
    
    /**
     * Sanitize settings
     */
    public function sanitize_settings($input) {
        $sanitized = array();
        
        // Sanitize enabled tools
        if (isset($input['enabled_tools']) && is_array($input['enabled_tools'])) {
            $sanitized['enabled_tools'] = array_map('sanitize_text_field', $input['enabled_tools']);
        } else {
            $sanitized['enabled_tools'] = array();
        }
        
        // Sanitize custom CSS
        $sanitized['custom_css'] = isset($input['custom_css']) ? wp_strip_all_tags($input['custom_css']) : '';
        
        // Sanitize custom JS
        $sanitized['custom_js'] = isset($input['custom_js']) ? wp_strip_all_tags($input['custom_js']) : '';
        
        // Sanitize display settings
        if (isset($input['display_settings'])) {
            $sanitized['display_settings'] = array(
                'show_branding' => isset($input['display_settings']['show_branding']),
                'theme' => sanitize_text_field($input['display_settings']['theme']),
                'color_scheme' => sanitize_text_field($input['display_settings']['color_scheme'])
            );
        }
        
        // Sanitize API settings
        if (isset($input['api_settings'])) {
            $sanitized['api_settings'] = array(
                'rate_limit' => absint($input['api_settings']['rate_limit']),
                'timeout' => absint($input['api_settings']['timeout']),
                'user_agent' => sanitize_text_field($input['api_settings']['user_agent'])
            );
        }
        
        // Sanitize security settings
        if (isset($input['security_settings'])) {
            $sanitized['security_settings'] = array(
                'require_login' => isset($input['security_settings']['require_login']),
                'allowed_roles' => isset($input['security_settings']['allowed_roles']) ? 
                    array_map('sanitize_text_field', $input['security_settings']['allowed_roles']) : array(),
                'enable_captcha' => isset($input['security_settings']['enable_captcha'])
            );
        }
        
        return array_merge($this->get_options(), $sanitized);
    }
    
    /**
     * Get options
     */
    public function get_options() {
        return wp_parse_args(get_option($this->option_name, array()), $this->defaults);
    }
    
    /**
     * Get specific option
     */
    public function get_option($key, $default = null) {
        $options = $this->get_options();
        return isset($options[$key]) ? $options[$key] : $default;
    }
    
    /**
     * Update option
     */
    public function update_option($key, $value) {
        $options = $this->get_options();
        $options[$key] = $value;
        return update_option($this->option_name, $options);
    }
}
