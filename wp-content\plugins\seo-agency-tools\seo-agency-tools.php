<?php
/**
 * Plugin Name: SEO Agency Tools
 * Plugin URI: https://your-website.com
 * Description: A comprehensive collection of SEO and website analysis tools for agencies and professionals.
 * Version: 1.0.0
 * Author: Your Name
 * License: GPL v2 or later
 * Text Domain: seo-agency-tools
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('SEO_AGENCY_TOOLS_VERSION', '1.0.0');
define('SEO_AGENCY_TOOLS_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('SEO_AGENCY_TOOLS_PLUGIN_URL', plugin_dir_url(__FILE__));
define('SEO_AGENCY_TOOLS_PLUGIN_FILE', __FILE__);

/**
 * Main SEO Agency Tools Class
 */
class SEO_Agency_Tools {
    
    /**
     * Instance of this class
     */
    private static $instance = null;
    
    /**
     * Available tools
     */
    private $tools = array();
    
    /**
     * Get instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * Initialize the plugin
     */
    private function init() {
        // Load dependencies
        $this->load_dependencies();
        
        // Initialize hooks
        add_action('init', array($this, 'init_hooks'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        
        // Register activation/deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        
        // Initialize tools
        $this->init_tools();
        
        // Register shortcodes
        $this->register_shortcodes();
    }
    
    /**
     * Load plugin dependencies
     */
    private function load_dependencies() {
        require_once SEO_AGENCY_TOOLS_PLUGIN_DIR . 'includes/class-database.php';
        require_once SEO_AGENCY_TOOLS_PLUGIN_DIR . 'includes/class-settings.php';
        require_once SEO_AGENCY_TOOLS_PLUGIN_DIR . 'includes/class-shortcodes.php';
        require_once SEO_AGENCY_TOOLS_PLUGIN_DIR . 'admin/class-admin.php';
        
        // Load tool classes
        $tool_files = glob(SEO_AGENCY_TOOLS_PLUGIN_DIR . 'tools/class-*.php');
        foreach ($tool_files as $file) {
            require_once $file;
        }
    }
    
    /**
     * Initialize hooks
     */
    public function init_hooks() {
        // Add AJAX handlers
        add_action('wp_ajax_seo_tools_run', array($this, 'handle_ajax_request'));
        add_action('wp_ajax_nopriv_seo_tools_run', array($this, 'handle_ajax_request'));
        
        // Load text domain
        load_plugin_textdomain('seo-agency-tools', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    /**
     * Initialize tools
     */
    private function init_tools() {
        $this->tools = array(
            'responsive_checker' => new SEO_Tools_Responsive_Checker(),
            'title_enhancer' => new SEO_Tools_Title_Enhancer(),
            'snippet_validator' => new SEO_Tools_Snippet_Validator(),
            'anchor_generator' => new SEO_Tools_Anchor_Generator(),
            'lazy_loading_tester' => new SEO_Tools_Lazy_Loading_Tester(),
            'bulk_meta_validator' => new SEO_Tools_Bulk_Meta_Validator(),
            'meta_validator' => new SEO_Tools_Meta_Validator(),
            'sitemap_finder' => new SEO_Tools_Sitemap_Finder(),
            'mx_lookup' => new SEO_Tools_MX_Lookup(),
            'http2_checker' => new SEO_Tools_HTTP2_Checker(),
            'cache_checker' => new SEO_Tools_Cache_Checker(),
            'subdomain_finder' => new SEO_Tools_Subdomain_Finder(),
            'ttfb_checker' => new SEO_Tools_TTFB_Checker(),
            'header_checker' => new SEO_Tools_Header_Checker(),
            'redirect_checker' => new SEO_Tools_Redirect_Checker()
        );
    }
    
    /**
     * Register shortcodes
     */
    private function register_shortcodes() {
        $shortcodes = new SEO_Tools_Shortcodes($this->tools);
        $shortcodes->register_all();
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('SEO Agency Tools', 'seo-agency-tools'),
            __('SEO Tools', 'seo-agency-tools'),
            'manage_options',
            'seo-agency-tools',
            array($this, 'admin_page'),
            'dashicons-chart-line',
            30
        );
        
        add_submenu_page(
            'seo-agency-tools',
            __('Settings', 'seo-agency-tools'),
            __('Settings', 'seo-agency-tools'),
            'manage_options',
            'seo-agency-tools-settings',
            array($this, 'settings_page')
        );
    }
    
    /**
     * Admin page callback
     */
    public function admin_page() {
        $admin = new SEO_Tools_Admin();
        $admin->render_main_page();
    }
    
    /**
     * Settings page callback
     */
    public function settings_page() {
        $admin = new SEO_Tools_Admin();
        $admin->render_settings_page();
    }
    
    /**
     * Enqueue frontend scripts
     */
    public function enqueue_frontend_scripts() {
        wp_enqueue_style(
            'seo-agency-tools-frontend',
            SEO_AGENCY_TOOLS_PLUGIN_URL . 'assets/css/frontend.css',
            array(),
            SEO_AGENCY_TOOLS_VERSION
        );
        
        wp_enqueue_script(
            'seo-agency-tools-frontend',
            SEO_AGENCY_TOOLS_PLUGIN_URL . 'assets/js/frontend.js',
            array('jquery'),
            SEO_AGENCY_TOOLS_VERSION,
            true
        );
        
        // Localize script for AJAX
        wp_localize_script('seo-agency-tools-frontend', 'seoToolsAjax', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('seo_tools_nonce'),
            'loading' => __('Loading...', 'seo-agency-tools'),
            'error' => __('An error occurred. Please try again.', 'seo-agency-tools')
        ));
    }
    
    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'seo-agency-tools') === false) {
            return;
        }
        
        wp_enqueue_style(
            'seo-agency-tools-admin',
            SEO_AGENCY_TOOLS_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            SEO_AGENCY_TOOLS_VERSION
        );
        
        wp_enqueue_script(
            'seo-agency-tools-admin',
            SEO_AGENCY_TOOLS_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery', 'wp-color-picker'),
            SEO_AGENCY_TOOLS_VERSION,
            true
        );
        
        wp_enqueue_style('wp-color-picker');
    }
    
    /**
     * Handle AJAX requests
     */
    public function handle_ajax_request() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'seo_tools_nonce')) {
            wp_die(__('Security check failed', 'seo-agency-tools'));
        }
        
        $tool = sanitize_text_field($_POST['tool']);
        $data = $_POST['data'];
        
        if (isset($this->tools[$tool])) {
            $result = $this->tools[$tool]->process($data);
            wp_send_json_success($result);
        } else {
            wp_send_json_error(__('Tool not found', 'seo-agency-tools'));
        }
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        $database = new SEO_Tools_Database();
        $database->create_tables();
        
        // Set default options
        $default_options = array(
            'enabled_tools' => array_keys($this->tools),
            'custom_css' => '',
            'custom_js' => ''
        );
        
        add_option('seo_agency_tools_options', $default_options);
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Get tools
     */
    public function get_tools() {
        return $this->tools;
    }
}

// Initialize the plugin
SEO_Agency_Tools::get_instance();
