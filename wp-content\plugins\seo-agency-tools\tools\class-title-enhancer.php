<?php
/**
 * Page Title Enhancer Tool
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

require_once SEO_AGENCY_TOOLS_PLUGIN_DIR . 'tools/class-base-tool.php';

class SEO_Tools_Title_Enhancer extends SEO_Tools_Base_Tool {
    
    /**
     * Initialize tool
     */
    protected function init() {
        $this->name = __('Page Title Enhancer', 'seo-agency-tools');
        $this->description = __('Analyze and optimize page titles for better SEO performance.', 'seo-agency-tools');
        $this->icon = 'dashicons-edit';
        $this->category = 'seo';
    }
    
    /**
     * Process tool request
     */
    public function process($data) {
        // Check rate limit
        if (!$this->check_rate_limit()) {
            return array(
                'success' => false,
                'error' => __('Rate limit exceeded. Please try again later.', 'seo-agency-tools')
            );
        }
        
        $url = isset($data['url']) ? sanitize_url($data['url']) : '';
        $keywords = isset($data['keywords']) ? sanitize_text_field($data['keywords']) : '';
        
        if (empty($url)) {
            return array(
                'success' => false,
                'error' => __('URL is required.', 'seo-agency-tools')
            );
        }
        
        $url = $this->validate_url($url);
        if (!$url) {
            return array(
                'success' => false,
                'error' => __('Invalid URL format.', 'seo-agency-tools')
            );
        }
        
        // Analyze page title
        $results = $this->analyze_page_title($url, $keywords);
        
        // Save results
        $this->save_result($url, $results);
        
        return array(
            'success' => true,
            'data' => $results
        );
    }
    
    /**
     * Analyze page title
     */
    private function analyze_page_title($url, $keywords = '') {
        $results = array(
            'url' => $url,
            'timestamp' => current_time('mysql'),
            'current_title' => '',
            'title_analysis' => array(),
            'suggestions' => array(),
            'keyword_analysis' => array(),
            'competitors' => array(),
            'score' => 0
        );
        
        // Get page content
        $response = $this->make_request($url);
        if (!$response['success']) {
            $results['error'] = $response['error'];
            return $results;
        }
        
        $html = $response['body'];
        
        // Extract current title
        $current_title = $this->extract_title($html);
        $results['current_title'] = $current_title;
        
        if (empty($current_title)) {
            $results['title_analysis']['missing_title'] = true;
            $results['suggestions'][] = __('Add a title tag to your page', 'seo-agency-tools');
            return $results;
        }
        
        // Analyze title
        $results['title_analysis'] = $this->analyze_title_structure($current_title);
        
        // Keyword analysis
        if (!empty($keywords)) {
            $results['keyword_analysis'] = $this->analyze_keywords_in_title($current_title, $keywords);
        }
        
        // Generate suggestions
        $results['suggestions'] = $this->generate_title_suggestions($current_title, $keywords, $results['title_analysis']);
        
        // Calculate score
        $results['score'] = $this->calculate_title_score($results['title_analysis'], $results['keyword_analysis']);
        
        return $results;
    }
    
    /**
     * Analyze title structure
     */
    private function analyze_title_structure($title) {
        $analysis = array(
            'length' => strlen($title),
            'word_count' => str_word_count($title),
            'character_count' => mb_strlen($title, 'UTF-8'),
            'has_brand' => false,
            'has_separator' => false,
            'starts_with_keyword' => false,
            'has_numbers' => false,
            'has_special_chars' => false,
            'readability' => array(),
            'issues' => array(),
            'strengths' => array()
        );
        
        // Length analysis
        if ($analysis['length'] < 30) {
            $analysis['issues'][] = __('Title is too short', 'seo-agency-tools');
        } elseif ($analysis['length'] > 60) {
            $analysis['issues'][] = __('Title may be truncated in search results', 'seo-agency-tools');
        } else {
            $analysis['strengths'][] = __('Good title length', 'seo-agency-tools');
        }
        
        // Check for separators
        $separators = array('|', '-', '–', '—', ':', '•');
        foreach ($separators as $sep) {
            if (strpos($title, $sep) !== false) {
                $analysis['has_separator'] = true;
                break;
            }
        }
        
        // Check for numbers
        if (preg_match('/\d/', $title)) {
            $analysis['has_numbers'] = true;
            $analysis['strengths'][] = __('Contains numbers (can improve CTR)', 'seo-agency-tools');
        }
        
        // Check for special characters
        if (preg_match('/[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]/', $title)) {
            $analysis['has_special_chars'] = true;
        }
        
        // Readability analysis
        $analysis['readability'] = $this->analyze_title_readability($title);
        
        // Check for common brand patterns
        $brand_patterns = array(
            '/\|\s*[A-Z][a-zA-Z\s]+$/',  // | Brand Name
            '/\-\s*[A-Z][a-zA-Z\s]+$/',  // - Brand Name
            '/:\s*[A-Z][a-zA-Z\s]+$/'    // : Brand Name
        );
        
        foreach ($brand_patterns as $pattern) {
            if (preg_match($pattern, $title)) {
                $analysis['has_brand'] = true;
                break;
            }
        }
        
        return $analysis;
    }
    
    /**
     * Analyze title readability
     */
    private function analyze_title_readability($title) {
        $readability = array(
            'capitalization' => 'unknown',
            'sentence_case' => false,
            'title_case' => false,
            'all_caps' => false,
            'mixed_case' => false
        );
        
        // Check capitalization patterns
        if ($title === strtoupper($title)) {
            $readability['all_caps'] = true;
            $readability['capitalization'] = 'all_caps';
        } elseif ($title === ucwords(strtolower($title))) {
            $readability['title_case'] = true;
            $readability['capitalization'] = 'title_case';
        } elseif ($title === ucfirst(strtolower($title))) {
            $readability['sentence_case'] = true;
            $readability['capitalization'] = 'sentence_case';
        } else {
            $readability['mixed_case'] = true;
            $readability['capitalization'] = 'mixed_case';
        }
        
        return $readability;
    }
    
    /**
     * Analyze keywords in title
     */
    private function analyze_keywords_in_title($title, $keywords) {
        $keyword_list = array_map('trim', explode(',', $keywords));
        $title_lower = strtolower($title);
        
        $analysis = array(
            'keywords_found' => array(),
            'keywords_missing' => array(),
            'keyword_positions' => array(),
            'keyword_density' => 0,
            'primary_keyword_first' => false
        );
        
        foreach ($keyword_list as $index => $keyword) {
            $keyword = trim($keyword);
            if (empty($keyword)) continue;
            
            $keyword_lower = strtolower($keyword);
            $position = strpos($title_lower, $keyword_lower);
            
            if ($position !== false) {
                $analysis['keywords_found'][] = $keyword;
                $analysis['keyword_positions'][$keyword] = $position;
                
                // Check if primary keyword (first in list) is at the beginning
                if ($index === 0 && $position === 0) {
                    $analysis['primary_keyword_first'] = true;
                }
            } else {
                $analysis['keywords_missing'][] = $keyword;
            }
        }
        
        // Calculate keyword density
        $total_words = str_word_count($title);
        $keyword_words = 0;
        foreach ($analysis['keywords_found'] as $keyword) {
            $keyword_words += str_word_count($keyword);
        }
        
        $analysis['keyword_density'] = $total_words > 0 ? round(($keyword_words / $total_words) * 100, 2) : 0;
        
        return $analysis;
    }
    
    /**
     * Generate title suggestions
     */
    private function generate_title_suggestions($current_title, $keywords, $analysis) {
        $suggestions = array();
        
        // Length suggestions
        if ($analysis['length'] < 30) {
            $suggestions[] = array(
                'type' => 'length',
                'priority' => 'high',
                'suggestion' => __('Expand your title to 30-60 characters for better SEO', 'seo-agency-tools'),
                'example' => $this->generate_longer_title($current_title, $keywords)
            );
        } elseif ($analysis['length'] > 60) {
            $suggestions[] = array(
                'type' => 'length',
                'priority' => 'medium',
                'suggestion' => __('Shorten your title to avoid truncation in search results', 'seo-agency-tools'),
                'example' => $this->generate_shorter_title($current_title)
            );
        }
        
        // Keyword suggestions
        if (!empty($keywords)) {
            $keyword_list = array_map('trim', explode(',', $keywords));
            $primary_keyword = $keyword_list[0];
            
            if (!empty($primary_keyword) && strpos(strtolower($current_title), strtolower($primary_keyword)) === false) {
                $suggestions[] = array(
                    'type' => 'keyword',
                    'priority' => 'high',
                    'suggestion' => sprintf(__('Include your primary keyword "%s" in the title', 'seo-agency-tools'), $primary_keyword),
                    'example' => $this->generate_keyword_optimized_title($current_title, $primary_keyword)
                );
            }
        }
        
        // Structure suggestions
        if (!$analysis['has_separator'] && $analysis['word_count'] > 3) {
            $suggestions[] = array(
                'type' => 'structure',
                'priority' => 'low',
                'suggestion' => __('Consider using separators (|, -, :) to improve readability', 'seo-agency-tools'),
                'example' => $this->add_separator_to_title($current_title)
            );
        }
        
        // Readability suggestions
        if ($analysis['readability']['all_caps']) {
            $suggestions[] = array(
                'type' => 'readability',
                'priority' => 'medium',
                'suggestion' => __('Avoid using all caps - use title case instead', 'seo-agency-tools'),
                'example' => ucwords(strtolower($current_title))
            );
        }
        
        // CTR improvement suggestions
        if (!$analysis['has_numbers']) {
            $suggestions[] = array(
                'type' => 'ctr',
                'priority' => 'low',
                'suggestion' => __('Consider adding numbers to improve click-through rates', 'seo-agency-tools'),
                'example' => $this->add_numbers_to_title($current_title)
            );
        }
        
        return $suggestions;
    }
    
    /**
     * Generate longer title
     */
    private function generate_longer_title($title, $keywords) {
        $extensions = array(
            __('Complete Guide', 'seo-agency-tools'),
            __('Best Practices', 'seo-agency-tools'),
            __('Tips & Tricks', 'seo-agency-tools'),
            __('Ultimate Guide', 'seo-agency-tools')
        );
        
        $extension = $extensions[array_rand($extensions)];
        return $title . ' - ' . $extension;
    }
    
    /**
     * Generate shorter title
     */
    private function generate_shorter_title($title) {
        // Remove common filler words and phrases
        $filler_words = array(
            'the complete guide to',
            'ultimate guide to',
            'everything you need to know about',
            'a comprehensive guide to',
            'the best way to',
            'how to effectively'
        );
        
        $shorter_title = $title;
        foreach ($filler_words as $filler) {
            $shorter_title = str_ireplace($filler, '', $shorter_title);
        }
        
        return trim($shorter_title);
    }
    
    /**
     * Generate keyword optimized title
     */
    private function generate_keyword_optimized_title($title, $keyword) {
        return $keyword . ' - ' . $title;
    }
    
    /**
     * Add separator to title
     */
    private function add_separator_to_title($title) {
        $words = explode(' ', $title);
        $mid_point = ceil(count($words) / 2);
        
        $first_part = implode(' ', array_slice($words, 0, $mid_point));
        $second_part = implode(' ', array_slice($words, $mid_point));
        
        return $first_part . ' | ' . $second_part;
    }
    
    /**
     * Add numbers to title
     */
    private function add_numbers_to_title($title) {
        $numbers = array('5', '7', '10', '15', '20');
        $number = $numbers[array_rand($numbers)];
        
        return $number . ' ' . $title;
    }
    
    /**
     * Calculate title score
     */
    private function calculate_title_score($title_analysis, $keyword_analysis) {
        $score = 0;
        $max_score = 100;
        
        // Length score (30 points)
        if ($title_analysis['length'] >= 30 && $title_analysis['length'] <= 60) {
            $score += 30;
        } elseif ($title_analysis['length'] >= 20 && $title_analysis['length'] < 30) {
            $score += 20;
        } elseif ($title_analysis['length'] > 60 && $title_analysis['length'] <= 70) {
            $score += 20;
        } else {
            $score += 10;
        }
        
        // Keyword score (40 points)
        if (!empty($keyword_analysis)) {
            if (!empty($keyword_analysis['keywords_found'])) {
                $score += 20;
                
                if ($keyword_analysis['primary_keyword_first']) {
                    $score += 20;
                } else {
                    $score += 10;
                }
            }
        } else {
            $score += 20; // No keywords provided, so we can't penalize
        }
        
        // Structure score (20 points)
        if ($title_analysis['has_separator']) {
            $score += 10;
        }
        
        if (!$title_analysis['readability']['all_caps']) {
            $score += 10;
        }
        
        // Bonus points (10 points)
        if ($title_analysis['has_numbers']) {
            $score += 5;
        }
        
        if (count($title_analysis['issues']) === 0) {
            $score += 5;
        }
        
        return min($score, $max_score);
    }
    
    /**
     * Render tool form
     */
    public function render_form($atts = array()) {
        $tool_name = $this->get_tool_name();
        
        $html = '<form class="seo-tool-form" data-tool="' . esc_attr($tool_name) . '">';
        $html .= '<div class="form-group">';
        $html .= '<label for="url">' . __('Website URL:', 'seo-agency-tools') . '</label>';
        $html .= '<input type="url" name="url" placeholder="' . __('Enter URL to analyze...', 'seo-agency-tools') . '" required>';
        $html .= '</div>';
        
        $html .= '<div class="form-group">';
        $html .= '<label for="keywords">' . __('Target Keywords (optional):', 'seo-agency-tools') . '</label>';
        $html .= '<input type="text" name="keywords" placeholder="' . __('Enter keywords separated by commas...', 'seo-agency-tools') . '">';
        $html .= '<small class="form-text">' . __('Enter your target keywords to get keyword-specific suggestions.', 'seo-agency-tools') . '</small>';
        $html .= '</div>';
        
        $button_text = isset($atts['button_text']) ? $atts['button_text'] : __('Analyze Title', 'seo-agency-tools');
        $html .= '<div class="form-group">';
        $html .= '<button type="submit" class="seo-tool-submit-btn">' . esc_html($button_text) . '</button>';
        $html .= '</div>';
        
        $html .= wp_nonce_field('seo_tools_nonce', 'nonce', true, false);
        $html .= '</form>';
        
        return $html;
    }
}
