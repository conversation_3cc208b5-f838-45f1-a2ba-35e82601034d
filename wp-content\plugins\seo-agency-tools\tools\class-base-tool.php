<?php
/**
 * Base tool class for SEO Agency Tools
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

abstract class SEO_Tools_Base_Tool {
    
    /**
     * Tool name
     */
    protected $name;
    
    /**
     * Tool description
     */
    protected $description;
    
    /**
     * Tool icon
     */
    protected $icon;
    
    /**
     * Tool category
     */
    protected $category;
    
    /**
     * Database instance
     */
    protected $database;
    
    /**
     * Settings instance
     */
    protected $settings;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new SEO_Tools_Database();
        $this->settings = new SEO_Tools_Settings();
        $this->init();
    }
    
    /**
     * Initialize tool
     */
    abstract protected function init();
    
    /**
     * Process tool request
     */
    abstract public function process($data);
    
    /**
     * Render tool form
     */
    abstract public function render_form($atts = array());
    
    /**
     * Get tool information
     */
    public function get_info() {
        return array(
            'name' => $this->name,
            'description' => $this->description,
            'icon' => $this->icon,
            'category' => $this->category
        );
    }
    
    /**
     * Validate URL
     */
    protected function validate_url($url) {
        // Add protocol if missing
        if (!preg_match('/^https?:\/\//', $url)) {
            $url = 'http://' . $url;
        }
        
        // Validate URL format
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }
        
        return $url;
    }
    
    /**
     * Make HTTP request
     */
    protected function make_request($url, $args = array()) {
        $default_args = array(
            'timeout' => 30,
            'user-agent' => 'SEO Agency Tools/1.0',
            'headers' => array(),
            'sslverify' => false
        );
        
        $args = wp_parse_args($args, $default_args);
        
        // Get API settings
        $api_settings = $this->settings->get_option('api_settings', array());
        if (isset($api_settings['timeout'])) {
            $args['timeout'] = $api_settings['timeout'];
        }
        if (isset($api_settings['user_agent'])) {
            $args['user-agent'] = $api_settings['user_agent'];
        }
        
        $response = wp_remote_get($url, $args);
        
        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'error' => $response->get_error_message()
            );
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        $headers = wp_remote_retrieve_headers($response);
        $body = wp_remote_retrieve_body($response);
        
        return array(
            'success' => true,
            'status_code' => $status_code,
            'headers' => $headers,
            'body' => $body,
            'response' => $response
        );
    }
    
    /**
     * Save result to database
     */
    protected function save_result($url, $result_data, $status = 'completed') {
        $tool_name = $this->get_tool_name();
        return $this->database->save_result($tool_name, $url, $result_data, $status);
    }
    
    /**
     * Get tool name from class name
     */
    protected function get_tool_name() {
        $class_name = get_class($this);
        $tool_name = str_replace('SEO_Tools_', '', $class_name);
        return strtolower(preg_replace('/([a-z])([A-Z])/', '$1_$2', $tool_name));
    }
    
    /**
     * Format file size
     */
    protected function format_file_size($bytes) {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }
    
    /**
     * Format time duration
     */
    protected function format_duration($seconds) {
        if ($seconds >= 60) {
            $minutes = floor($seconds / 60);
            $seconds = $seconds % 60;
            return sprintf('%dm %ds', $minutes, $seconds);
        } else {
            return number_format($seconds, 3) . 's';
        }
    }
    
    /**
     * Extract domain from URL
     */
    protected function extract_domain($url) {
        $parsed = parse_url($url);
        return isset($parsed['host']) ? $parsed['host'] : '';
    }
    
    /**
     * Check if URL is accessible
     */
    protected function check_url_accessibility($url) {
        $response = $this->make_request($url, array('timeout' => 10));
        
        if (!$response['success']) {
            return array(
                'accessible' => false,
                'error' => $response['error']
            );
        }
        
        $status_code = $response['status_code'];
        
        return array(
            'accessible' => $status_code >= 200 && $status_code < 400,
            'status_code' => $status_code,
            'redirect' => $status_code >= 300 && $status_code < 400
        );
    }
    
    /**
     * Parse HTML content
     */
    protected function parse_html($html) {
        $dom = new DOMDocument();
        libxml_use_internal_errors(true);
        $dom->loadHTML($html);
        libxml_clear_errors();
        return $dom;
    }
    
    /**
     * Extract meta tags from HTML
     */
    protected function extract_meta_tags($html) {
        $dom = $this->parse_html($html);
        $meta_tags = array();
        
        $metas = $dom->getElementsByTagName('meta');
        foreach ($metas as $meta) {
            $name = $meta->getAttribute('name') ?: $meta->getAttribute('property');
            $content = $meta->getAttribute('content');
            
            if ($name && $content) {
                $meta_tags[$name] = $content;
            }
        }
        
        return $meta_tags;
    }
    
    /**
     * Extract title from HTML
     */
    protected function extract_title($html) {
        $dom = $this->parse_html($html);
        $titles = $dom->getElementsByTagName('title');
        
        if ($titles->length > 0) {
            return trim($titles->item(0)->textContent);
        }
        
        return '';
    }
    
    /**
     * Check rate limit
     */
    protected function check_rate_limit($ip = null) {
        if (!$ip) {
            $ip = $_SERVER['REMOTE_ADDR'];
        }
        
        $api_settings = $this->settings->get_option('api_settings', array());
        $rate_limit = isset($api_settings['rate_limit']) ? $api_settings['rate_limit'] : 100;
        
        $transient_key = 'seo_tools_rate_limit_' . md5($ip);
        $current_count = get_transient($transient_key);
        
        if ($current_count === false) {
            set_transient($transient_key, 1, HOUR_IN_SECONDS);
            return true;
        }
        
        if ($current_count >= $rate_limit) {
            return false;
        }
        
        set_transient($transient_key, $current_count + 1, HOUR_IN_SECONDS);
        return true;
    }
    
    /**
     * Render error message
     */
    protected function render_error($message) {
        return '<div class="seo-tool-error">' . esc_html($message) . '</div>';
    }
    
    /**
     * Render success message
     */
    protected function render_success($message) {
        return '<div class="seo-tool-success">' . esc_html($message) . '</div>';
    }
    
    /**
     * Render warning message
     */
    protected function render_warning($message) {
        return '<div class="seo-tool-warning">' . esc_html($message) . '</div>';
    }
    
    /**
     * Render info message
     */
    protected function render_info($message) {
        return '<div class="seo-tool-info">' . esc_html($message) . '</div>';
    }
    
    /**
     * Render loading indicator
     */
    protected function render_loading($message = '') {
        if (empty($message)) {
            $message = __('Loading...', 'seo-agency-tools');
        }
        return '<div class="seo-tool-loading">' . esc_html($message) . '</div>';
    }
    
    /**
     * Render progress bar
     */
    protected function render_progress($percentage, $label = '') {
        $percentage = max(0, min(100, $percentage));
        
        $html = '<div class="seo-tool-progress">';
        $html .= '<div class="progress-bar">';
        $html .= '<div class="progress-fill" style="width: ' . $percentage . '%"></div>';
        $html .= '</div>';
        
        if (!empty($label)) {
            $html .= '<div class="progress-label">' . esc_html($label) . '</div>';
        }
        
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Render result table
     */
    protected function render_result_table($data, $headers = array()) {
        if (empty($data)) {
            return $this->render_info(__('No data to display.', 'seo-agency-tools'));
        }
        
        $html = '<div class="seo-tool-table-wrapper">';
        $html .= '<table class="seo-tool-table">';
        
        // Headers
        if (!empty($headers)) {
            $html .= '<thead><tr>';
            foreach ($headers as $header) {
                $html .= '<th>' . esc_html($header) . '</th>';
            }
            $html .= '</tr></thead>';
        }
        
        // Body
        $html .= '<tbody>';
        foreach ($data as $row) {
            $html .= '<tr>';
            if (is_array($row)) {
                foreach ($row as $cell) {
                    $html .= '<td>' . esc_html($cell) . '</td>';
                }
            } else {
                $html .= '<td>' . esc_html($row) . '</td>';
            }
            $html .= '</tr>';
        }
        $html .= '</tbody>';
        
        $html .= '</table>';
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Render basic form
     */
    protected function render_basic_form($atts = array()) {
        $placeholder = isset($atts['placeholder']) ? $atts['placeholder'] : __('Enter URL...', 'seo-agency-tools');
        $button_text = isset($atts['button_text']) ? $atts['button_text'] : __('Analyze', 'seo-agency-tools');
        $tool_name = $this->get_tool_name();
        
        $html = '<form class="seo-tool-form" data-tool="' . esc_attr($tool_name) . '">';
        $html .= '<div class="form-group">';
        $html .= '<input type="url" name="url" placeholder="' . esc_attr($placeholder) . '" required>';
        $html .= '<button type="submit" class="seo-tool-submit-btn">' . esc_html($button_text) . '</button>';
        $html .= '</div>';
        $html .= wp_nonce_field('seo_tools_nonce', 'nonce', true, false);
        $html .= '</form>';
        
        return $html;
    }
}
