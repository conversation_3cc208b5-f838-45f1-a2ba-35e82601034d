/* SEO Agency Tools - Frontend Styles */

/* Base Styles */
.seo-tool-container {
    max-width: 100%;
    margin: 20px 0;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.seo-tool-container * {
    box-sizing: border-box;
}

/* Header Styles */
.seo-tool-header {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.seo-tool-title {
    margin: 0 0 5px 0;
    font-size: 24px;
    font-weight: 600;
    color: #333;
}

.seo-tool-description {
    margin-bottom: 20px;
}

.seo-tool-description p {
    margin: 0;
    color: #666;
    line-height: 1.5;
}

/* Form Styles */
.seo-tool-form {
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.form-group input[type="text"],
.form-group input[type="url"],
.form-group input[type="email"],
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.1);
}

.form-group .form-text {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: #666;
}

/* Button Styles */
.seo-tool-submit-btn,
.seo-tool-btn {
    display: inline-block;
    padding: 10px 20px;
    background: #0073aa;
    color: #fff;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.seo-tool-submit-btn:hover,
.seo-tool-btn:hover {
    background: #005a87;
    color: #fff;
}

.seo-tool-submit-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* Results Styles */
.seo-tool-results {
    margin-top: 20px;
}

.seo-tool-loading {
    text-align: center;
    padding: 20px;
    color: #666;
}

.seo-tool-loading::before {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
    vertical-align: middle;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.seo-tool-output {
    padding: 15px;
    background: #f9f9f9;
    border-radius: 4px;
    border-left: 4px solid #0073aa;
}

/* Message Styles */
.seo-tool-error {
    padding: 10px 15px;
    background: #fff2f2;
    border: 1px solid #ff6b6b;
    border-radius: 4px;
    color: #d63031;
    margin: 10px 0;
}

.seo-tool-success {
    padding: 10px 15px;
    background: #f0fff4;
    border: 1px solid #00b894;
    border-radius: 4px;
    color: #00b894;
    margin: 10px 0;
}

.seo-tool-warning {
    padding: 10px 15px;
    background: #fffbf0;
    border: 1px solid #fdcb6e;
    border-radius: 4px;
    color: #e17055;
    margin: 10px 0;
}

.seo-tool-info {
    padding: 10px 15px;
    background: #f0f8ff;
    border: 1px solid #74b9ff;
    border-radius: 4px;
    color: #0984e3;
    margin: 10px 0;
}

/* Table Styles */
.seo-tool-table-wrapper {
    overflow-x: auto;
    margin: 15px 0;
}

.seo-tool-table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
}

.seo-tool-table th,
.seo-tool-table td {
    padding: 10px 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.seo-tool-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.seo-tool-table tr:hover {
    background: #f8f9fa;
}

/* Progress Bar */
.seo-tool-progress {
    margin: 15px 0;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #eee;
    border-radius: 10px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00b894, #0984e3);
    transition: width 0.3s ease;
}

.progress-label {
    text-align: center;
    margin-top: 5px;
    font-size: 12px;
    color: #666;
}

/* Tools List Styles */
.seo-tools-list {
    display: grid;
    gap: 20px;
    margin: 20px 0;
}

.seo-tools-list.columns-1 { grid-template-columns: 1fr; }
.seo-tools-list.columns-2 { grid-template-columns: repeat(2, 1fr); }
.seo-tools-list.columns-3 { grid-template-columns: repeat(3, 1fr); }
.seo-tools-list.columns-4 { grid-template-columns: repeat(4, 1fr); }

.seo-tool-item {
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: #fff;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.seo-tool-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.seo-tool-icon {
    font-size: 48px;
    color: #0073aa;
    margin-bottom: 15px;
}

.seo-tool-name {
    margin: 0 0 10px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.seo-tool-desc {
    margin: 0 0 15px 0;
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

.seo-tool-launch-btn {
    padding: 8px 16px;
    background: #0073aa;
    color: #fff;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.seo-tool-launch-btn:hover {
    background: #005a87;
}

/* Theme Variations */
.theme-dark {
    background: #2c3e50;
    border-color: #34495e;
    color: #ecf0f1;
}

.theme-dark .seo-tool-title {
    color: #ecf0f1;
}

.theme-dark .seo-tool-description p {
    color: #bdc3c7;
}

.theme-dark .form-group input,
.theme-dark .form-group textarea,
.theme-dark .form-group select {
    background: #34495e;
    border-color: #4a5f7a;
    color: #ecf0f1;
}

.theme-dark .seo-tool-output {
    background: #34495e;
    border-left-color: #3498db;
}

.theme-light {
    background: #fafafa;
    border-color: #e0e0e0;
}

.theme-minimal {
    border: none;
    box-shadow: none;
    background: transparent;
}

/* Responsive Design */
@media (max-width: 768px) {
    .seo-tool-container {
        margin: 10px 0;
        padding: 15px;
    }
    
    .seo-tools-list.columns-3,
    .seo-tools-list.columns-4 {
        grid-template-columns: 1fr;
    }
    
    .seo-tools-list.columns-2 {
        grid-template-columns: 1fr;
    }
    
    .form-group input,
    .form-group textarea {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

@media (max-width: 480px) {
    .seo-tool-title {
        font-size: 20px;
    }
    
    .seo-tool-item {
        padding: 15px;
    }
    
    .seo-tool-icon {
        font-size: 36px;
    }
}

/* Color Schemes */
.color-scheme-green .seo-tool-submit-btn,
.color-scheme-green .seo-tool-btn {
    background: #00b894;
}

.color-scheme-green .seo-tool-submit-btn:hover,
.color-scheme-green .seo-tool-btn:hover {
    background: #00a085;
}

.color-scheme-red .seo-tool-submit-btn,
.color-scheme-red .seo-tool-btn {
    background: #e17055;
}

.color-scheme-red .seo-tool-submit-btn:hover,
.color-scheme-red .seo-tool-btn:hover {
    background: #d63031;
}

.color-scheme-purple .seo-tool-submit-btn,
.color-scheme-purple .seo-tool-btn {
    background: #a29bfe;
}

.color-scheme-purple .seo-tool-submit-btn:hover,
.color-scheme-purple .seo-tool-btn:hover {
    background: #6c5ce7;
}

.color-scheme-orange .seo-tool-submit-btn,
.color-scheme-orange .seo-tool-btn {
    background: #fd79a8;
}

.color-scheme-orange .seo-tool-submit-btn:hover,
.color-scheme-orange .seo-tool-btn:hover {
    background: #e84393;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 10px; }
.mb-2 { margin-bottom: 20px; }
.mb-3 { margin-bottom: 30px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 10px; }
.mt-2 { margin-top: 20px; }
.mt-3 { margin-top: 30px; }
