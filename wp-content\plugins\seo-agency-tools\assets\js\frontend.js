/**
 * SEO Agency Tools - Frontend JavaScript
 */

(function($) {
    'use strict';

    // Main SEO Tools object
    window.SEOTools = {
        init: function() {
            this.bindEvents();
            this.initTooltips();
            this.initCopyButtons();
        },

        bindEvents: function() {
            // Handle form submissions
            $(document).on('submit', '.seo-tool-form', this.handleFormSubmit);
            
            // Handle tool launch buttons
            $(document).on('click', '.seo-tool-launch-btn', this.handleToolLaunch);
            
            // Handle copy buttons
            $(document).on('click', '.copy-shortcode-btn', this.handleCopyShortcode);
            
            // Handle result actions
            $(document).on('click', '.view-result-btn', this.handleViewResult);
            $(document).on('click', '.delete-result-btn', this.handleDeleteResult);
        },

        handleFormSubmit: function(e) {
            e.preventDefault();
            
            var $form = $(this);
            var $container = $form.closest('.seo-tool-container');
            var $results = $container.find('.seo-tool-results');
            var $loading = $results.find('.seo-tool-loading');
            var $output = $results.find('.seo-tool-output');
            var $submitBtn = $form.find('.seo-tool-submit-btn');
            
            var tool = $form.data('tool') || $container.data('tool');
            var formData = SEOTools.serializeFormData($form);
            
            // Validate required fields
            var isValid = true;
            $form.find('[required]').each(function() {
                if (!$(this).val().trim()) {
                    isValid = false;
                    $(this).addClass('error');
                } else {
                    $(this).removeClass('error');
                }
            });
            
            if (!isValid) {
                SEOTools.showMessage($container, 'Please fill in all required fields.', 'error');
                return;
            }
            
            // Show loading state
            $results.show();
            $loading.show();
            $output.empty();
            $submitBtn.prop('disabled', true).text(seoToolsAjax.loading);
            
            // Make AJAX request
            $.ajax({
                url: seoToolsAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'seo_tools_run',
                    tool: tool,
                    data: formData,
                    nonce: seoToolsAjax.nonce
                },
                success: function(response) {
                    $loading.hide();
                    $submitBtn.prop('disabled', false).text($submitBtn.data('original-text') || 'Analyze');
                    
                    if (response.success) {
                        SEOTools.displayResults($output, response.data, tool);
                    } else {
                        SEOTools.showMessage($container, response.data || seoToolsAjax.error, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    $loading.hide();
                    $submitBtn.prop('disabled', false).text($submitBtn.data('original-text') || 'Analyze');
                    SEOTools.showMessage($container, seoToolsAjax.error, 'error');
                    console.error('SEO Tools Error:', error);
                }
            });
        },

        handleToolLaunch: function(e) {
            e.preventDefault();
            
            var tool = $(this).data('tool');
            var $container = $(this).closest('.seo-tool-item');
            
            // Create a modal or redirect to tool page
            SEOTools.openToolModal(tool);
        },

        handleCopyShortcode: function(e) {
            e.preventDefault();
            
            var shortcode = $(this).data('shortcode');
            var $btn = $(this);
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(shortcode).then(function() {
                    SEOTools.showCopySuccess($btn);
                });
            } else {
                // Fallback for older browsers
                var textArea = document.createElement('textarea');
                textArea.value = shortcode;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                SEOTools.showCopySuccess($btn);
            }
        },

        handleViewResult: function(e) {
            e.preventDefault();
            
            var resultId = $(this).data('result-id');
            SEOTools.viewResult(resultId);
        },

        handleDeleteResult: function(e) {
            e.preventDefault();
            
            if (!confirm('Are you sure you want to delete this result?')) {
                return;
            }
            
            var resultId = $(this).data('result-id');
            var $row = $(this).closest('tr');
            
            $.ajax({
                url: seoToolsAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'seo_tools_delete_result',
                    result_id: resultId,
                    nonce: seoToolsAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $row.fadeOut(300, function() {
                            $(this).remove();
                        });
                    } else {
                        alert(response.data || 'Failed to delete result');
                    }
                },
                error: function() {
                    alert('An error occurred while deleting the result');
                }
            });
        },

        serializeFormData: function($form) {
            var data = {};
            $form.find('input, textarea, select').each(function() {
                var $field = $(this);
                var name = $field.attr('name');
                var value = $field.val();
                
                if (name && name !== 'nonce') {
                    if ($field.attr('type') === 'checkbox') {
                        data[name] = $field.is(':checked');
                    } else if ($field.attr('type') === 'radio') {
                        if ($field.is(':checked')) {
                            data[name] = value;
                        }
                    } else {
                        data[name] = value;
                    }
                }
            });
            return data;
        },

        displayResults: function($output, data, tool) {
            var html = '';
            
            switch (tool) {
                case 'responsive_checker':
                    html = SEOTools.renderResponsiveResults(data);
                    break;
                case 'title_enhancer':
                    html = SEOTools.renderTitleResults(data);
                    break;
                default:
                    html = SEOTools.renderGenericResults(data);
            }
            
            $output.html(html);
            SEOTools.initResultsInteractions($output);
        },

        renderResponsiveResults: function(data) {
            var html = '<div class="responsive-results">';
            
            // Summary
            html += '<div class="result-summary">';
            html += '<h4>Responsive Analysis Summary</h4>';
            html += '<p><strong>URL:</strong> ' + data.url + '</p>';
            html += '<p><strong>Mobile Friendly:</strong> ' + (data.mobile_friendly ? '✅ Yes' : '❌ No') + '</p>';
            html += '</div>';
            
            // Meta viewport
            if (data.meta_viewport) {
                html += '<div class="meta-viewport-section">';
                html += '<h5>Meta Viewport</h5>';
                if (data.meta_viewport.present) {
                    html += '<p>✅ Meta viewport tag found</p>';
                    html += '<code>' + data.meta_viewport.content + '</code>';
                } else {
                    html += '<p>❌ Meta viewport tag missing</p>';
                }
                html += '</div>';
            }
            
            // Issues and recommendations
            if (data.issues && data.issues.length > 0) {
                html += '<div class="issues-section">';
                html += '<h5>Issues Found</h5>';
                html += '<ul>';
                data.issues.forEach(function(issue) {
                    html += '<li class="issue-item">❌ ' + issue + '</li>';
                });
                html += '</ul>';
                html += '</div>';
            }
            
            if (data.recommendations && data.recommendations.length > 0) {
                html += '<div class="recommendations-section">';
                html += '<h5>Recommendations</h5>';
                html += '<ul>';
                data.recommendations.forEach(function(rec) {
                    html += '<li class="recommendation-item">💡 ' + rec + '</li>';
                });
                html += '</ul>';
                html += '</div>';
            }
            
            html += '</div>';
            return html;
        },

        renderTitleResults: function(data) {
            var html = '<div class="title-results">';
            
            // Current title
            html += '<div class="current-title-section">';
            html += '<h4>Current Title Analysis</h4>';
            html += '<div class="title-display">';
            html += '<strong>Title:</strong> ' + (data.current_title || 'No title found') + '<br>';
            html += '<strong>Length:</strong> ' + (data.title_analysis ? data.title_analysis.length : 0) + ' characters<br>';
            html += '<strong>Score:</strong> ' + data.score + '/100';
            html += '</div>';
            html += '</div>';
            
            // Title analysis
            if (data.title_analysis) {
                html += '<div class="title-analysis-section">';
                html += '<h5>Analysis Details</h5>';
                html += '<ul>';
                
                if (data.title_analysis.length >= 30 && data.title_analysis.length <= 60) {
                    html += '<li>✅ Good title length (' + data.title_analysis.length + ' characters)</li>';
                } else if (data.title_analysis.length < 30) {
                    html += '<li>⚠️ Title is too short (' + data.title_analysis.length + ' characters)</li>';
                } else {
                    html += '<li>⚠️ Title may be truncated (' + data.title_analysis.length + ' characters)</li>';
                }
                
                if (data.title_analysis.has_separator) {
                    html += '<li>✅ Contains separators for better readability</li>';
                }
                
                if (data.title_analysis.has_numbers) {
                    html += '<li>✅ Contains numbers (good for CTR)</li>';
                }
                
                html += '</ul>';
                html += '</div>';
            }
            
            // Keyword analysis
            if (data.keyword_analysis && data.keyword_analysis.keywords_found) {
                html += '<div class="keyword-analysis-section">';
                html += '<h5>Keyword Analysis</h5>';
                
                if (data.keyword_analysis.keywords_found.length > 0) {
                    html += '<p><strong>Keywords Found:</strong> ' + data.keyword_analysis.keywords_found.join(', ') + '</p>';
                }
                
                if (data.keyword_analysis.keywords_missing.length > 0) {
                    html += '<p><strong>Missing Keywords:</strong> ' + data.keyword_analysis.keywords_missing.join(', ') + '</p>';
                }
                
                html += '<p><strong>Keyword Density:</strong> ' + data.keyword_analysis.keyword_density + '%</p>';
                html += '</div>';
            }
            
            // Suggestions
            if (data.suggestions && data.suggestions.length > 0) {
                html += '<div class="suggestions-section">';
                html += '<h5>Improvement Suggestions</h5>';
                data.suggestions.forEach(function(suggestion) {
                    html += '<div class="suggestion-item priority-' + suggestion.priority + '">';
                    html += '<p><strong>' + suggestion.suggestion + '</strong></p>';
                    if (suggestion.example) {
                        html += '<p><em>Example:</em> ' + suggestion.example + '</p>';
                    }
                    html += '</div>';
                });
                html += '</div>';
            }
            
            html += '</div>';
            return html;
        },

        renderGenericResults: function(data) {
            var html = '<div class="generic-results">';
            html += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            html += '</div>';
            return html;
        },

        initResultsInteractions: function($container) {
            // Add any interactive elements to results
            $container.find('.copy-result-btn').on('click', function() {
                var text = $(this).data('copy-text');
                SEOTools.copyToClipboard(text);
            });
        },

        showMessage: function($container, message, type) {
            var $message = $('<div class="seo-tool-' + type + '">' + message + '</div>');
            $container.prepend($message);
            
            setTimeout(function() {
                $message.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 5000);
        },

        showCopySuccess: function($btn) {
            var originalText = $btn.text();
            $btn.text('Copied!').addClass('copied');
            
            setTimeout(function() {
                $btn.text(originalText).removeClass('copied');
            }, 2000);
        },

        copyToClipboard: function(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text);
            } else {
                var textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
            }
        },

        openToolModal: function(tool) {
            // Create a simple modal for tool
            var modal = $('<div class="seo-tool-modal">');
            var content = $('<div class="seo-tool-modal-content">');
            var close = $('<span class="seo-tool-modal-close">&times;</span>');
            
            content.append(close);
            content.append('<h3>Loading tool...</h3>');
            modal.append(content);
            
            $('body').append(modal);
            modal.show();
            
            // Close modal
            close.on('click', function() {
                modal.remove();
            });
            
            modal.on('click', function(e) {
                if (e.target === modal[0]) {
                    modal.remove();
                }
            });
        },

        viewResult: function(resultId) {
            // Open result in modal or new window
            console.log('Viewing result:', resultId);
        },

        initTooltips: function() {
            // Initialize tooltips if needed
            $('[data-tooltip]').each(function() {
                var $this = $(this);
                var tooltip = $this.data('tooltip');
                
                $this.on('mouseenter', function() {
                    var $tooltip = $('<div class="seo-tooltip">' + tooltip + '</div>');
                    $('body').append($tooltip);
                    
                    var offset = $this.offset();
                    $tooltip.css({
                        top: offset.top - $tooltip.outerHeight() - 5,
                        left: offset.left + ($this.outerWidth() / 2) - ($tooltip.outerWidth() / 2)
                    });
                });
                
                $this.on('mouseleave', function() {
                    $('.seo-tooltip').remove();
                });
            });
        },

        initCopyButtons: function() {
            // Store original button text
            $('.seo-tool-submit-btn').each(function() {
                $(this).data('original-text', $(this).text());
            });
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        SEOTools.init();
    });

})(jQuery);
