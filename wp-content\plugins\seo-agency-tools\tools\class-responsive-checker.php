<?php
/**
 * Responsive Design Checker Tool
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

require_once SEO_AGENCY_TOOLS_PLUGIN_DIR . 'tools/class-base-tool.php';

class SEO_Tools_Responsive_Checker extends SEO_Tools_Base_Tool {
    
    /**
     * Initialize tool
     */
    protected function init() {
        $this->name = __('Responsive Design Checker', 'seo-agency-tools');
        $this->description = __('Check how your website looks on different screen sizes and devices.', 'seo-agency-tools');
        $this->icon = 'dashicons-smartphone';
        $this->category = 'design';
    }
    
    /**
     * Process tool request
     */
    public function process($data) {
        // Check rate limit
        if (!$this->check_rate_limit()) {
            return array(
                'success' => false,
                'error' => __('Rate limit exceeded. Please try again later.', 'seo-agency-tools')
            );
        }
        
        $url = isset($data['url']) ? sanitize_url($data['url']) : '';
        
        if (empty($url)) {
            return array(
                'success' => false,
                'error' => __('URL is required.', 'seo-agency-tools')
            );
        }
        
        $url = $this->validate_url($url);
        if (!$url) {
            return array(
                'success' => false,
                'error' => __('Invalid URL format.', 'seo-agency-tools')
            );
        }
        
        // Check if URL is accessible
        $accessibility = $this->check_url_accessibility($url);
        if (!$accessibility['accessible']) {
            return array(
                'success' => false,
                'error' => __('URL is not accessible: ', 'seo-agency-tools') . $accessibility['error']
            );
        }
        
        // Perform responsive checks
        $results = $this->check_responsive_design($url);
        
        // Save results
        $this->save_result($url, $results);
        
        return array(
            'success' => true,
            'data' => $results
        );
    }
    
    /**
     * Check responsive design
     */
    private function check_responsive_design($url) {
        $viewports = array(
            'mobile' => array('width' => 375, 'height' => 667, 'name' => 'Mobile (iPhone)'),
            'tablet' => array('width' => 768, 'height' => 1024, 'name' => 'Tablet (iPad)'),
            'desktop' => array('width' => 1920, 'height' => 1080, 'name' => 'Desktop'),
            'large_desktop' => array('width' => 2560, 'height' => 1440, 'name' => 'Large Desktop')
        );
        
        $results = array(
            'url' => $url,
            'timestamp' => current_time('mysql'),
            'viewports' => array(),
            'meta_viewport' => null,
            'css_media_queries' => array(),
            'responsive_images' => false,
            'mobile_friendly' => false,
            'issues' => array(),
            'recommendations' => array()
        );
        
        // Get page content
        $response = $this->make_request($url);
        if (!$response['success']) {
            $results['error'] = $response['error'];
            return $results;
        }
        
        $html = $response['body'];
        $headers = $response['headers'];
        
        // Check meta viewport tag
        $results['meta_viewport'] = $this->check_meta_viewport($html);
        
        // Check CSS media queries
        $results['css_media_queries'] = $this->check_css_media_queries($html, $url);
        
        // Check responsive images
        $results['responsive_images'] = $this->check_responsive_images($html);
        
        // Simulate different viewports
        foreach ($viewports as $key => $viewport) {
            $viewport_result = $this->simulate_viewport($url, $viewport);
            $results['viewports'][$key] = $viewport_result;
        }
        
        // Analyze results and generate recommendations
        $results = $this->analyze_responsive_results($results);
        
        return $results;
    }
    
    /**
     * Check meta viewport tag
     */
    private function check_meta_viewport($html) {
        $meta_tags = $this->extract_meta_tags($html);
        
        if (isset($meta_tags['viewport'])) {
            $viewport_content = $meta_tags['viewport'];
            
            return array(
                'present' => true,
                'content' => $viewport_content,
                'has_width' => strpos($viewport_content, 'width=') !== false,
                'has_device_width' => strpos($viewport_content, 'width=device-width') !== false,
                'has_initial_scale' => strpos($viewport_content, 'initial-scale=') !== false,
                'valid' => strpos($viewport_content, 'width=device-width') !== false
            );
        }
        
        return array(
            'present' => false,
            'content' => '',
            'has_width' => false,
            'has_device_width' => false,
            'has_initial_scale' => false,
            'valid' => false
        );
    }
    
    /**
     * Check CSS media queries
     */
    private function check_css_media_queries($html, $url) {
        $media_queries = array();
        $dom = $this->parse_html($html);
        
        // Check inline styles
        $style_tags = $dom->getElementsByTagName('style');
        foreach ($style_tags as $style) {
            $css_content = $style->textContent;
            $queries = $this->extract_media_queries_from_css($css_content);
            $media_queries = array_merge($media_queries, $queries);
        }
        
        // Check external stylesheets
        $link_tags = $dom->getElementsByTagName('link');
        foreach ($link_tags as $link) {
            if ($link->getAttribute('rel') === 'stylesheet') {
                $href = $link->getAttribute('href');
                $media = $link->getAttribute('media');
                
                if ($media && $media !== 'all') {
                    $media_queries[] = array(
                        'type' => 'link',
                        'media' => $media,
                        'href' => $href
                    );
                }
                
                // Try to fetch and analyze external CSS
                if ($href) {
                    $css_url = $this->resolve_relative_url($href, $url);
                    $css_response = $this->make_request($css_url, array('timeout' => 10));
                    
                    if ($css_response['success']) {
                        $queries = $this->extract_media_queries_from_css($css_response['body']);
                        $media_queries = array_merge($media_queries, $queries);
                    }
                }
            }
        }
        
        return $media_queries;
    }
    
    /**
     * Extract media queries from CSS
     */
    private function extract_media_queries_from_css($css) {
        $media_queries = array();
        
        // Match @media rules
        preg_match_all('/@media\s+([^{]+)\s*\{/', $css, $matches);
        
        foreach ($matches[1] as $query) {
            $media_queries[] = array(
                'type' => 'css',
                'query' => trim($query)
            );
        }
        
        return $media_queries;
    }
    
    /**
     * Check responsive images
     */
    private function check_responsive_images($html) {
        $dom = $this->parse_html($html);
        $images = $dom->getElementsByTagName('img');
        
        $total_images = $images->length;
        $responsive_images = 0;
        
        foreach ($images as $img) {
            $srcset = $img->getAttribute('srcset');
            $sizes = $img->getAttribute('sizes');
            
            if ($srcset || $sizes) {
                $responsive_images++;
            }
        }
        
        return array(
            'total_images' => $total_images,
            'responsive_images' => $responsive_images,
            'percentage' => $total_images > 0 ? round(($responsive_images / $total_images) * 100, 2) : 0,
            'has_responsive_images' => $responsive_images > 0
        );
    }
    
    /**
     * Simulate viewport
     */
    private function simulate_viewport($url, $viewport) {
        // This is a simplified simulation
        // In a real implementation, you might use a headless browser like Puppeteer
        
        $user_agents = array(
            'mobile' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
            'tablet' => 'Mozilla/5.0 (iPad; CPU OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
            'desktop' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        );
        
        $device_type = $viewport['width'] <= 480 ? 'mobile' : ($viewport['width'] <= 768 ? 'tablet' : 'desktop');
        $user_agent = isset($user_agents[$device_type]) ? $user_agents[$device_type] : $user_agents['desktop'];
        
        $response = $this->make_request($url, array(
            'user-agent' => $user_agent,
            'headers' => array(
                'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
            )
        ));
        
        if (!$response['success']) {
            return array(
                'viewport' => $viewport,
                'error' => $response['error'],
                'accessible' => false
            );
        }
        
        $html = $response['body'];
        
        return array(
            'viewport' => $viewport,
            'accessible' => true,
            'content_length' => strlen($html),
            'load_time' => 0, // Would need actual timing in real implementation
            'has_horizontal_scroll' => $this->check_horizontal_scroll($html, $viewport['width']),
            'text_readable' => $this->check_text_readability($html),
            'touch_friendly' => $this->check_touch_friendliness($html)
        );
    }
    
    /**
     * Check for horizontal scroll issues
     */
    private function check_horizontal_scroll($html, $viewport_width) {
        // Simplified check - look for fixed widths larger than viewport
        $dom = $this->parse_html($html);
        
        // Check for elements with fixed widths
        $xpath = new DOMXPath($dom);
        $elements_with_style = $xpath->query('//*[@style]');
        
        foreach ($elements_with_style as $element) {
            $style = $element->getAttribute('style');
            if (preg_match('/width:\s*(\d+)px/', $style, $matches)) {
                $width = intval($matches[1]);
                if ($width > $viewport_width) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * Check text readability
     */
    private function check_text_readability($html) {
        $dom = $this->parse_html($html);
        
        // Check for minimum font sizes
        $xpath = new DOMXPath($dom);
        $text_elements = $xpath->query('//p | //span | //div[text()]');
        
        $readable_count = 0;
        $total_count = 0;
        
        foreach ($text_elements as $element) {
            $style = $element->getAttribute('style');
            if (preg_match('/font-size:\s*(\d+)px/', $style, $matches)) {
                $font_size = intval($matches[1]);
                $total_count++;
                if ($font_size >= 14) {
                    $readable_count++;
                }
            }
        }
        
        return array(
            'readable_elements' => $readable_count,
            'total_elements' => $total_count,
            'percentage' => $total_count > 0 ? round(($readable_count / $total_count) * 100, 2) : 100
        );
    }
    
    /**
     * Check touch friendliness
     */
    private function check_touch_friendliness($html) {
        $dom = $this->parse_html($html);
        
        // Check for touch-friendly button sizes
        $buttons = $dom->getElementsByTagName('button');
        $links = $dom->getElementsByTagName('a');
        
        $touch_friendly_count = 0;
        $total_interactive = $buttons->length + $links->length;
        
        // This is a simplified check
        // In reality, you'd need to calculate actual rendered sizes
        
        return array(
            'touch_friendly_elements' => $touch_friendly_count,
            'total_interactive_elements' => $total_interactive,
            'percentage' => $total_interactive > 0 ? round(($touch_friendly_count / $total_interactive) * 100, 2) : 100
        );
    }
    
    /**
     * Analyze responsive results
     */
    private function analyze_responsive_results($results) {
        $issues = array();
        $recommendations = array();
        
        // Check meta viewport
        if (!$results['meta_viewport']['present']) {
            $issues[] = __('Missing meta viewport tag', 'seo-agency-tools');
            $recommendations[] = __('Add <meta name="viewport" content="width=device-width, initial-scale=1"> to your HTML head', 'seo-agency-tools');
        } elseif (!$results['meta_viewport']['valid']) {
            $issues[] = __('Invalid meta viewport configuration', 'seo-agency-tools');
            $recommendations[] = __('Use width=device-width in your viewport meta tag', 'seo-agency-tools');
        }
        
        // Check media queries
        if (empty($results['css_media_queries'])) {
            $issues[] = __('No CSS media queries found', 'seo-agency-tools');
            $recommendations[] = __('Add CSS media queries to make your design responsive', 'seo-agency-tools');
        }
        
        // Check responsive images
        if (!$results['responsive_images']['has_responsive_images']) {
            $issues[] = __('No responsive images found', 'seo-agency-tools');
            $recommendations[] = __('Use srcset and sizes attributes for responsive images', 'seo-agency-tools');
        }
        
        // Determine mobile friendliness
        $mobile_friendly = empty($issues) && 
                          $results['meta_viewport']['valid'] && 
                          !empty($results['css_media_queries']);
        
        $results['issues'] = $issues;
        $results['recommendations'] = $recommendations;
        $results['mobile_friendly'] = $mobile_friendly;
        
        return $results;
    }
    
    /**
     * Resolve relative URL
     */
    private function resolve_relative_url($relative_url, $base_url) {
        if (filter_var($relative_url, FILTER_VALIDATE_URL)) {
            return $relative_url;
        }
        
        $base_parts = parse_url($base_url);
        
        if (strpos($relative_url, '//') === 0) {
            return $base_parts['scheme'] . ':' . $relative_url;
        }
        
        if (strpos($relative_url, '/') === 0) {
            return $base_parts['scheme'] . '://' . $base_parts['host'] . $relative_url;
        }
        
        $base_path = isset($base_parts['path']) ? dirname($base_parts['path']) : '';
        return $base_parts['scheme'] . '://' . $base_parts['host'] . $base_path . '/' . $relative_url;
    }
    
    /**
     * Render tool form
     */
    public function render_form($atts = array()) {
        $atts['placeholder'] = __('Enter website URL to check responsiveness...', 'seo-agency-tools');
        $atts['button_text'] = __('Check Responsiveness', 'seo-agency-tools');
        
        return $this->render_basic_form($atts);
    }
}
