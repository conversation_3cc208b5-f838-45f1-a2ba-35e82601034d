/* SEO Agency Tools - Admin Styles */

/* Dashboard Widgets */
.dashboard-widgets {
    display: grid;
    gap: 20px;
    margin-top: 20px;
}

.dashboard-widget-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.dashboard-widget {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,0.04);
}

.dashboard-widget h3 {
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 600;
    color: #23282d;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 4px solid #0073aa;
}

.stat-number {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: #0073aa;
    margin-bottom: 5px;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Popular Tools */
.popular-tools {
    max-height: 200px;
    overflow-y: auto;
}

.tool-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.tool-stat:last-child {
    border-bottom: none;
}

.tool-name {
    font-weight: 500;
    color: #23282d;
}

.tool-count {
    background: #0073aa;
    color: #fff;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: 600;
}

/* Quick Actions */
.quick-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.quick-actions .button {
    flex: 1;
    min-width: 120px;
    text-align: center;
}

/* System Status */
.system-status {
    display: grid;
    gap: 10px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
}

.status-item.status-good {
    background: #d4edda;
    border-left: 4px solid #28a745;
}

.status-item.status-warning {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
}

.status-item.status-error {
    background: #f8d7da;
    border-left: 4px solid #dc3545;
}

.status-label {
    font-weight: 500;
    color: #23282d;
}

.status-value {
    color: #666;
}

/* Tools Management */
.tools-management {
    margin-top: 20px;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.tool-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
}

.tool-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.tool-card.enabled {
    border-left: 4px solid #28a745;
}

.tool-card.disabled {
    border-left: 4px solid #dc3545;
    opacity: 0.7;
}

.tool-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.tool-icon {
    font-size: 32px;
    color: #0073aa;
    margin-right: 15px;
}

.tool-title h3 {
    margin: 0 0 5px 0;
    font-size: 16px;
    font-weight: 600;
    color: #23282d;
}

.tool-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.tool-card.enabled .tool-status {
    background: #d4edda;
    color: #28a745;
}

.tool-card.disabled .tool-status {
    background: #f8d7da;
    color: #dc3545;
}

.tool-description {
    margin-bottom: 15px;
}

.tool-description p {
    margin: 0;
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

.tool-actions {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.tool-actions .button {
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
}

.tool-shortcode {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    font-size: 12px;
    border-left: 3px solid #0073aa;
}

.tool-shortcode strong {
    display: block;
    margin-bottom: 5px;
    color: #23282d;
}

.tool-shortcode code {
    background: #fff;
    padding: 4px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    color: #d63384;
}

.copy-shortcode-btn {
    margin-left: 8px;
    font-size: 11px;
    padding: 2px 6px;
    background: #0073aa;
    color: #fff;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.copy-shortcode-btn:hover {
    background: #005a87;
}

.copy-shortcode-btn.copied {
    background: #28a745;
}

/* Results Management */
.results-management {
    margin-top: 20px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.results-filters form {
    display: flex;
    gap: 10px;
    align-items: center;
}

.results-table-wrapper {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    overflow: hidden;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.status-completed {
    background: #d4edda;
    color: #28a745;
}

.status-badge.status-failed {
    background: #f8d7da;
    color: #dc3545;
}

.status-badge.status-pending {
    background: #fff3cd;
    color: #ffc107;
}

.no-results {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

/* Shortcodes Reference */
.shortcodes-reference {
    margin-top: 20px;
}

.shortcodes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.shortcode-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #0073aa;
}

.shortcode-card h3 {
    margin: 0 0 10px 0;
    font-size: 16px;
    color: #23282d;
}

.shortcode-card p {
    margin: 0 0 15px 0;
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

.shortcode-example {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.shortcode-example strong {
    display: block;
    margin-bottom: 8px;
    color: #23282d;
    font-size: 13px;
}

.shortcode-example code {
    background: #fff;
    padding: 6px 8px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    color: #d63384;
    font-size: 13px;
    display: inline-block;
    margin-right: 8px;
}

.shortcode-attributes {
    margin-top: 30px;
}

.shortcode-attributes h3 {
    margin-bottom: 15px;
    color: #23282d;
}

.shortcode-attributes table {
    width: 100%;
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
}

.shortcode-attributes th,
.shortcode-attributes td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.shortcode-attributes th {
    background: #f8f9fa;
    font-weight: 600;
    color: #23282d;
}

.shortcode-attributes code {
    background: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    color: #d63384;
    font-size: 12px;
}

/* Tool Checkboxes */
.tool-checkboxes {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 10px;
    margin: 15px 0;
}

.tool-checkboxes label {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.tool-checkboxes label:hover {
    background: #e9ecef;
}

.tool-checkboxes input[type="checkbox"] {
    margin-right: 8px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .dashboard-widget-row {
        grid-template-columns: 1fr;
    }
    
    .tools-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .quick-actions {
        flex-direction: column;
    }
    
    .quick-actions .button {
        flex: none;
        width: 100%;
    }
    
    .results-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .results-filters form {
        flex-direction: column;
        gap: 8px;
    }
    
    .tool-actions {
        flex-direction: column;
    }
    
    .shortcodes-grid {
        grid-template-columns: 1fr;
    }
    
    .tool-checkboxes {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .tools-grid {
        grid-template-columns: 1fr;
    }
    
    .tool-card {
        padding: 15px;
    }
    
    .tool-header {
        flex-direction: column;
        align-items: flex-start;
        text-align: center;
    }
    
    .tool-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #ccc;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Tooltips */
.seo-tooltip {
    position: absolute;
    background: #23282d;
    color: #fff;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1000;
    max-width: 200px;
    text-align: center;
}

.seo-tooltip::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #23282d transparent transparent transparent;
}

/* Form Enhancements */
.form-table th {
    width: 200px;
    padding: 20px 10px 20px 0;
}

.form-table td {
    padding: 15px 10px 20px 0;
}

.form-table textarea.large-text {
    width: 100%;
    max-width: 600px;
    height: 200px;
}

.form-table .description {
    margin-top: 5px;
    font-style: italic;
    color: #666;
}

/* Color Picker */
.wp-color-picker {
    width: 100px;
}

/* Success Messages */
.notice.notice-success {
    border-left-color: #28a745;
}

.notice.notice-error {
    border-left-color: #dc3545;
}

.notice.notice-warning {
    border-left-color: #ffc107;
}

.notice.notice-info {
    border-left-color: #17a2b8;
}
