/**
 * SEO Agency Tools - Admin JavaScript
 */

(function($) {
    'use strict';

    // Admin object
    window.SEOToolsAdmin = {
        init: function() {
            this.bindEvents();
            this.initColorPickers();
            this.initTooltips();
        },

        bindEvents: function() {
            // Tool toggle buttons
            $(document).on('click', '.tool-toggle-btn', this.handleToolToggle);

            // Tool test buttons
            $(document).on('click', '.tool-test-btn', this.handleToolTest);

            // Copy shortcode buttons
            $(document).on('click', '.copy-shortcode-btn', this.handleCopyShortcode);

            // View result buttons
            $(document).on('click', '.view-result-btn', this.handleViewResult);

            // Delete result buttons
            $(document).on('click', '.delete-result-btn', this.handleDeleteResult);

            // Clean old results
            $(document).on('click', '[onclick="seoToolsCleanOldResults()"]', this.handleCleanOldResults);

            // Settings form validation
            $(document).on('submit', '#seo-tools-settings-form', this.validateSettingsForm);

            // Tab switching is handled by WordPress page navigation
        },

        handleToolToggle: function(e) {
            e.preventDefault();

            var $btn = $(this);
            var tool = $btn.data('tool');
            var enabled = $btn.data('enabled') === '1' ? 0 : 1;
            var $card = $btn.closest('.tool-card');

            // Show loading state
            $btn.prop('disabled', true).text('Processing...');
            $card.addClass('loading');

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'seo_tools_toggle_tool',
                    tool: tool,
                    enabled: enabled,
                    nonce: $('#seo_tools_admin_nonce').val()
                },
                success: function(response) {
                    if (response.success) {
                        // Update button state
                        $btn.data('enabled', enabled);
                        $btn.text(enabled ? 'Disable' : 'Enable');

                        // Update card appearance
                        if (enabled) {
                            $card.removeClass('disabled').addClass('enabled');
                            $card.find('.tool-status').text('Enabled').removeClass().addClass('tool-status');
                            $card.find('.tool-test-btn, .tool-settings-btn').show();
                        } else {
                            $card.removeClass('enabled').addClass('disabled');
                            $card.find('.tool-status').text('Disabled').removeClass().addClass('tool-status');
                            $card.find('.tool-test-btn, .tool-settings-btn').hide();
                        }

                        SEOToolsAdmin.showNotice(response.data.message, 'success');
                    } else {
                        SEOToolsAdmin.showNotice(response.data || 'Failed to toggle tool', 'error');
                    }
                },
                error: function() {
                    SEOToolsAdmin.showNotice('An error occurred while toggling the tool', 'error');
                },
                complete: function() {
                    $btn.prop('disabled', false);
                    $card.removeClass('loading');
                }
            });
        },

        handleToolTest: function(e) {
            e.preventDefault();

            var tool = $(this).data('tool');
            var $btn = $(this);

            // Create test modal
            var modal = SEOToolsAdmin.createModal('Test Tool: ' + tool, 'Loading test interface...');

            // Load test form
            setTimeout(function() {
                var testForm = SEOToolsAdmin.createTestForm(tool);
                modal.find('.modal-body').html(testForm);
            }, 500);
        },

        handleCopyShortcode: function(e) {
            e.preventDefault();

            var shortcode = $(this).data('shortcode');
            var $btn = $(this);

            if (navigator.clipboard) {
                navigator.clipboard.writeText(shortcode).then(function() {
                    SEOToolsAdmin.showCopySuccess($btn);
                });
            } else {
                // Fallback for older browsers
                var textArea = document.createElement('textarea');
                textArea.value = shortcode;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                SEOToolsAdmin.showCopySuccess($btn);
            }
        },

        handleViewResult: function(e) {
            e.preventDefault();

            var resultId = $(this).data('result-id');

            // Create result modal
            var modal = SEOToolsAdmin.createModal('Analysis Result', 'Loading result...');

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'seo_tools_get_result',
                    result_id: resultId,
                    nonce: $('#seo_tools_admin_nonce').val()
                },
                success: function(response) {
                    if (response.success) {
                        var resultHtml = SEOToolsAdmin.formatResult(response.data);
                        modal.find('.modal-body').html(resultHtml);
                    } else {
                        modal.find('.modal-body').html('<p>Failed to load result.</p>');
                    }
                },
                error: function() {
                    modal.find('.modal-body').html('<p>An error occurred while loading the result.</p>');
                }
            });
        },

        handleDeleteResult: function(e) {
            e.preventDefault();

            if (!confirm('Are you sure you want to delete this result?')) {
                return;
            }

            var resultId = $(this).data('result-id');
            var $row = $(this).closest('tr');

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'seo_tools_delete_result',
                    result_id: resultId,
                    nonce: $('#seo_tools_admin_nonce').val()
                },
                success: function(response) {
                    if (response.success) {
                        $row.fadeOut(300, function() {
                            $(this).remove();
                        });
                        SEOToolsAdmin.showNotice(response.data.message, 'success');
                    } else {
                        SEOToolsAdmin.showNotice(response.data || 'Failed to delete result', 'error');
                    }
                },
                error: function() {
                    SEOToolsAdmin.showNotice('An error occurred while deleting the result', 'error');
                }
            });
        },

        handleCleanOldResults: function(e) {
            e.preventDefault();

            if (!confirm('Are you sure you want to clean old results? This will delete results older than 30 days.')) {
                return;
            }

            var $btn = $(this);
            $btn.prop('disabled', true).text('Cleaning...');

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'seo_tools_clean_old_results',
                    nonce: $('#seo_tools_admin_nonce').val()
                },
                success: function(response) {
                    if (response.success) {
                        SEOToolsAdmin.showNotice(response.data.message, 'success');
                        // Refresh the page to update the results table
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    } else {
                        SEOToolsAdmin.showNotice(response.data || 'Failed to clean old results', 'error');
                    }
                },
                error: function() {
                    SEOToolsAdmin.showNotice('An error occurred while cleaning old results', 'error');
                },
                complete: function() {
                    $btn.prop('disabled', false).text('Clean Old Results');
                }
            });
        },

        validateSettingsForm: function(e) {
            var isValid = true;
            var errors = [];

            // Validate custom CSS
            var customCSS = $('textarea[name*="custom_css"]').val();
            if (customCSS && !SEOToolsAdmin.isValidCSS(customCSS)) {
                errors.push('Custom CSS contains invalid syntax');
                isValid = false;
            }

            // Validate custom JS
            var customJS = $('textarea[name*="custom_js"]').val();
            if (customJS && !SEOToolsAdmin.isValidJS(customJS)) {
                errors.push('Custom JavaScript contains invalid syntax');
                isValid = false;
            }

            if (!isValid) {
                e.preventDefault();
                SEOToolsAdmin.showNotice('Please fix the following errors:\n' + errors.join('\n'), 'error');
            }
        },



        createModal: function(title, content) {
            var modal = $('<div class="seo-tools-modal">');
            var modalContent = $('<div class="seo-tools-modal-content">');
            var modalHeader = $('<div class="seo-tools-modal-header">');
            var modalBody = $('<div class="modal-body">');
            var closeBtn = $('<span class="seo-tools-modal-close">&times;</span>');

            modalHeader.append('<h3>' + title + '</h3>').append(closeBtn);
            modalBody.html(content);
            modalContent.append(modalHeader).append(modalBody);
            modal.append(modalContent);

            // Add to page
            $('body').append(modal);
            modal.show();

            // Close handlers
            closeBtn.on('click', function() {
                modal.remove();
            });

            modal.on('click', function(e) {
                if (e.target === modal[0]) {
                    modal.remove();
                }
            });

            return modal;
        },

        createTestForm: function(tool) {
            var form = '<form class="seo-tool-test-form" data-tool="' + tool + '">';
            form += '<div class="form-group">';
            form += '<label for="test-url">Test URL:</label>';
            form += '<input type="url" id="test-url" name="url" placeholder="Enter URL to test..." required>';
            form += '</div>';
            form += '<div class="form-group">';
            form += '<button type="submit" class="button button-primary">Run Test</button>';
            form += '</div>';
            form += '</form>';
            form += '<div class="test-results" style="display: none;"></div>';

            return form;
        },

        formatResult: function(result) {
            var html = '<div class="result-display">';
            html += '<h4>Analysis Result</h4>';
            html += '<p><strong>URL:</strong> ' + result.url + '</p>';
            html += '<p><strong>Date:</strong> ' + result.created_at + '</p>';
            html += '<p><strong>Status:</strong> ' + result.status + '</p>';
            html += '<div class="result-data">';
            html += '<pre>' + JSON.stringify(result.result_data, null, 2) + '</pre>';
            html += '</div>';
            html += '</div>';

            return html;
        },

        showNotice: function(message, type) {
            var notice = $('<div class="notice notice-' + type + ' is-dismissible">');
            notice.append('<p>' + message + '</p>');
            notice.append('<button type="button" class="notice-dismiss"><span class="screen-reader-text">Dismiss this notice.</span></button>');

            $('.wrap h1').after(notice);

            // Auto dismiss after 5 seconds
            setTimeout(function() {
                notice.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 5000);

            // Manual dismiss
            notice.find('.notice-dismiss').on('click', function() {
                notice.fadeOut(300, function() {
                    $(this).remove();
                });
            });
        },

        showCopySuccess: function($btn) {
            var originalText = $btn.text();
            $btn.text('Copied!').addClass('copied');

            setTimeout(function() {
                $btn.text(originalText).removeClass('copied');
            }, 2000);
        },

        isValidCSS: function(css) {
            // Basic CSS validation
            try {
                // Check for balanced braces
                var openBraces = (css.match(/\{/g) || []).length;
                var closeBraces = (css.match(/\}/g) || []).length;
                return openBraces === closeBraces;
            } catch (e) {
                return false;
            }
        },

        isValidJS: function(js) {
            // Basic JS validation
            try {
                new Function(js);
                return true;
            } catch (e) {
                return false;
            }
        },

        initColorPickers: function() {
            if ($.fn.wpColorPicker) {
                $('.color-picker').wpColorPicker();
            }
        },

        initTooltips: function() {
            $('[data-tooltip]').each(function() {
                var $this = $(this);
                var tooltip = $this.data('tooltip');

                $this.on('mouseenter', function() {
                    var $tooltip = $('<div class="seo-tooltip">' + tooltip + '</div>');
                    $('body').append($tooltip);

                    var offset = $this.offset();
                    $tooltip.css({
                        top: offset.top - $tooltip.outerHeight() - 10,
                        left: offset.left + ($this.outerWidth() / 2) - ($tooltip.outerWidth() / 2)
                    });
                });

                $this.on('mouseleave', function() {
                    $('.seo-tooltip').remove();
                });
            });
        }
    };

    // Global functions for onclick handlers
    window.seoToolsCleanOldResults = function() {
        SEOToolsAdmin.handleCleanOldResults({ preventDefault: function() {} });
    };

    // Initialize when document is ready
    $(document).ready(function() {
        SEOToolsAdmin.init();

        // Add admin nonce if not present
        if (!$('#seo_tools_admin_nonce').length) {
            $('body').append('<input type="hidden" id="seo_tools_admin_nonce" value="' + (window.seoToolsAdminNonce || '') + '">');
        }
    });

})(jQuery);

// Modal CSS (injected dynamically)
jQuery(document).ready(function($) {
    var modalCSS = `
        <style>
        .seo-tools-modal {
            display: none;
            position: fixed;
            z-index: 100000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .seo-tools-modal-content {
            background-color: #fff;
            margin: 5% auto;
            padding: 0;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .seo-tools-modal-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .seo-tools-modal-header h3 {
            margin: 0;
            color: #23282d;
        }

        .seo-tools-modal-close {
            color: #666;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .seo-tools-modal-close:hover {
            color: #000;
        }

        .modal-body {
            padding: 20px;
        }

        .seo-tool-test-form .form-group {
            margin-bottom: 15px;
        }

        .seo-tool-test-form label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .seo-tool-test-form input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .result-display pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            line-height: 1.4;
        }
        </style>
    `;

    $('head').append(modalCSS);
});
