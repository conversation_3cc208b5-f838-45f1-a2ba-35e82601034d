<!DOCTYPE html>
<html>
<head>
    <title>SEO Agency Tools Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .shortcode { background: #f5f5f5; padding: 10px; font-family: monospace; }
    </style>
</head>
<body>
    <h1>SEO Agency Tools - Test Page</h1>
    
    <div class="test-section">
        <h2>Available Shortcodes</h2>
        <p>Copy these shortcodes and paste them into any WordPress page or post:</p>
        
        <h3>1. Responsive Design Checker</h3>
        <div class="shortcode">[seo_responsive_checker]</div>
        
        <h3>2. Page Title Enhancer</h3>
        <div class="shortcode">[seo_title_enhancer]</div>
        
        <h3>3. Meta Tags Validator</h3>
        <div class="shortcode">[seo_meta_validator]</div>
        
        <h3>4. HTTP Header Checker</h3>
        <div class="shortcode">[seo_header_checker]</div>
        
        <h3>5. General Tool Shortcode</h3>
        <div class="shortcode">[seo_tool tool="responsive_checker"]</div>
        
        <h3>6. Tools List</h3>
        <div class="shortcode">[seo_tools_list columns="2"]</div>
    </div>
    
    <div class="test-section">
        <h2>Customization Examples</h2>
        
        <h3>Custom Title and Theme</h3>
        <div class="shortcode">[seo_responsive_checker title="Check Mobile Compatibility" theme="dark"]</div>
        
        <h3>Custom Button Text</h3>
        <div class="shortcode">[seo_title_enhancer button_text="Optimize My Title"]</div>
        
        <h3>Custom Width and Class</h3>
        <div class="shortcode">[seo_meta_validator width="80%" class="my-custom-class"]</div>
    </div>
    
    <div class="test-section">
        <h2>Testing Instructions</h2>
        <ol>
            <li>Go to your WordPress admin dashboard</li>
            <li>Navigate to <strong>SEO Tools</strong> in the admin menu</li>
            <li>Check the <strong>Dashboard</strong> tab for statistics</li>
            <li>Visit the <strong>Tools</strong> tab to enable/disable tools</li>
            <li>Check the <strong>Shortcodes</strong> tab for reference</li>
            <li>Create a new page/post and add any of the shortcodes above</li>
            <li>Test the tools by entering URLs and analyzing them</li>
        </ol>
    </div>
    
    <div class="test-section">
        <h2>Admin Features</h2>
        <ul>
            <li><strong>Dashboard:</strong> View usage statistics and system status</li>
            <li><strong>Tools:</strong> Enable/disable individual tools</li>
            <li><strong>Results:</strong> View and manage analysis results</li>
            <li><strong>Shortcodes:</strong> Copy shortcodes and view documentation</li>
            <li><strong>Settings:</strong> Customize appearance and behavior</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>Troubleshooting</h2>
        <ul>
            <li>If tabs don't switch: Clear browser cache and reload</li>
            <li>If tools don't load: Check if JavaScript is enabled</li>
            <li>If AJAX fails: Check WordPress admin-ajax.php is accessible</li>
            <li>If shortcodes don't work: Ensure tools are enabled in admin</li>
        </ul>
    </div>
</body>
</html>
