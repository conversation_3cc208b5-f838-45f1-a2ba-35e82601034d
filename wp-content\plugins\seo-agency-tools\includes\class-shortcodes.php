<?php
/**
 * Shortcodes management class for SEO Agency Tools
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SEO_Tools_Shortcodes {
    
    /**
     * Available tools
     */
    private $tools;
    
    /**
     * Settings instance
     */
    private $settings;
    
    /**
     * Constructor
     */
    public function __construct($tools) {
        $this->tools = $tools;
        $this->settings = new SEO_Tools_Settings();
    }
    
    /**
     * Register all shortcodes
     */
    public function register_all() {
        $enabled_tools = $this->settings->get_option('enabled_tools', array());
        
        foreach ($this->tools as $tool_key => $tool_instance) {
            if (in_array($tool_key, $enabled_tools)) {
                add_shortcode('seo_' . $tool_key, array($this, 'render_tool_shortcode'));
            }
        }
        
        // Register general shortcode
        add_shortcode('seo_tool', array($this, 'render_general_shortcode'));
        add_shortcode('seo_tools_list', array($this, 'render_tools_list_shortcode'));
    }
    
    /**
     * Render tool shortcode
     */
    public function render_tool_shortcode($atts, $content = '', $tag = '') {
        // Extract tool name from shortcode tag
        $tool_name = str_replace('seo_', '', $tag);
        
        // Default attributes
        $default_atts = array(
            'title' => '',
            'description' => '',
            'show_title' => 'true',
            'show_description' => 'true',
            'theme' => 'default',
            'width' => '100%',
            'height' => 'auto',
            'class' => '',
            'id' => '',
            'placeholder' => '',
            'button_text' => __('Analyze', 'seo-agency-tools'),
            'loading_text' => __('Analyzing...', 'seo-agency-tools'),
            'show_results' => 'true',
            'auto_submit' => 'false'
        );
        
        $atts = shortcode_atts($default_atts, $atts, $tag);
        
        // Check if tool exists
        if (!isset($this->tools[$tool_name])) {
            return '<div class="seo-tool-error">' . __('Tool not found.', 'seo-agency-tools') . '</div>';
        }
        
        // Check if user has permission
        if (!$this->check_user_permission()) {
            return '<div class="seo-tool-error">' . __('You do not have permission to use this tool.', 'seo-agency-tools') . '</div>';
        }
        
        // Generate unique ID if not provided
        if (empty($atts['id'])) {
            $atts['id'] = 'seo-tool-' . $tool_name . '-' . uniqid();
        }
        
        // Get tool instance
        $tool = $this->tools[$tool_name];
        
        // Start output buffering
        ob_start();
        
        // Render tool HTML
        $this->render_tool_html($tool, $tool_name, $atts);
        
        return ob_get_clean();
    }
    
    /**
     * Render general shortcode
     */
    public function render_general_shortcode($atts, $content = '') {
        $default_atts = array(
            'tool' => '',
            'title' => '',
            'description' => '',
            'show_title' => 'true',
            'show_description' => 'true',
            'theme' => 'default',
            'width' => '100%',
            'height' => 'auto',
            'class' => '',
            'id' => '',
            'placeholder' => '',
            'button_text' => __('Analyze', 'seo-agency-tools'),
            'loading_text' => __('Analyzing...', 'seo-agency-tools'),
            'show_results' => 'true',
            'auto_submit' => 'false'
        );
        
        $atts = shortcode_atts($default_atts, $atts, 'seo_tool');
        
        if (empty($atts['tool'])) {
            return '<div class="seo-tool-error">' . __('Tool parameter is required.', 'seo-agency-tools') . '</div>';
        }
        
        // Check if tool exists
        if (!isset($this->tools[$atts['tool']])) {
            return '<div class="seo-tool-error">' . __('Tool not found.', 'seo-agency-tools') . '</div>';
        }
        
        // Check if user has permission
        if (!$this->check_user_permission()) {
            return '<div class="seo-tool-error">' . __('You do not have permission to use this tool.', 'seo-agency-tools') . '</div>';
        }
        
        // Generate unique ID if not provided
        if (empty($atts['id'])) {
            $atts['id'] = 'seo-tool-' . $atts['tool'] . '-' . uniqid();
        }
        
        // Get tool instance
        $tool = $this->tools[$atts['tool']];
        
        // Start output buffering
        ob_start();
        
        // Render tool HTML
        $this->render_tool_html($tool, $atts['tool'], $atts);
        
        return ob_get_clean();
    }
    
    /**
     * Render tools list shortcode
     */
    public function render_tools_list_shortcode($atts, $content = '') {
        $default_atts = array(
            'columns' => '3',
            'show_descriptions' => 'true',
            'show_icons' => 'true',
            'theme' => 'grid',
            'class' => '',
            'exclude' => '',
            'include' => ''
        );
        
        $atts = shortcode_atts($default_atts, $atts, 'seo_tools_list');
        
        // Check if user has permission
        if (!$this->check_user_permission()) {
            return '<div class="seo-tool-error">' . __('You do not have permission to use these tools.', 'seo-agency-tools') . '</div>';
        }
        
        $enabled_tools = $this->settings->get_option('enabled_tools', array());
        
        // Filter tools based on include/exclude
        $tools_to_show = $enabled_tools;
        
        if (!empty($atts['include'])) {
            $include = array_map('trim', explode(',', $atts['include']));
            $tools_to_show = array_intersect($tools_to_show, $include);
        }
        
        if (!empty($atts['exclude'])) {
            $exclude = array_map('trim', explode(',', $atts['exclude']));
            $tools_to_show = array_diff($tools_to_show, $exclude);
        }
        
        // Start output buffering
        ob_start();
        
        // Render tools list
        $this->render_tools_list_html($tools_to_show, $atts);
        
        return ob_get_clean();
    }
    
    /**
     * Render tool HTML
     */
    private function render_tool_html($tool, $tool_name, $atts) {
        $tool_info = $tool->get_info();
        
        // Apply custom CSS class
        $css_classes = array('seo-tool-container', 'seo-tool-' . $tool_name);
        if (!empty($atts['class'])) {
            $css_classes[] = $atts['class'];
        }
        if (!empty($atts['theme'])) {
            $css_classes[] = 'theme-' . $atts['theme'];
        }
        
        $style = '';
        if (!empty($atts['width'])) {
            $style .= 'width: ' . $atts['width'] . ';';
        }
        if (!empty($atts['height']) && $atts['height'] !== 'auto') {
            $style .= 'height: ' . $atts['height'] . ';';
        }
        
        ?>
        <div id="<?php echo esc_attr($atts['id']); ?>" 
             class="<?php echo esc_attr(implode(' ', $css_classes)); ?>" 
             style="<?php echo esc_attr($style); ?>"
             data-tool="<?php echo esc_attr($tool_name); ?>">
            
            <?php if ($atts['show_title'] === 'true'): ?>
                <div class="seo-tool-header">
                    <h3 class="seo-tool-title">
                        <?php echo !empty($atts['title']) ? esc_html($atts['title']) : esc_html($tool_info['name']); ?>
                    </h3>
                </div>
            <?php endif; ?>
            
            <?php if ($atts['show_description'] === 'true'): ?>
                <div class="seo-tool-description">
                    <p><?php echo !empty($atts['description']) ? esc_html($atts['description']) : esc_html($tool_info['description']); ?></p>
                </div>
            <?php endif; ?>
            
            <div class="seo-tool-form">
                <?php echo $tool->render_form($atts); ?>
            </div>
            
            <?php if ($atts['show_results'] === 'true'): ?>
                <div class="seo-tool-results" style="display: none;">
                    <div class="seo-tool-loading" style="display: none;">
                        <span class="loading-text"><?php echo esc_html($atts['loading_text']); ?></span>
                    </div>
                    <div class="seo-tool-output"></div>
                </div>
            <?php endif; ?>
            
        </div>
        
        <?php if ($atts['auto_submit'] === 'true'): ?>
            <script>
            jQuery(document).ready(function($) {
                $('#<?php echo esc_js($atts['id']); ?> form').trigger('submit');
            });
            </script>
        <?php endif; ?>
        <?php
    }
    
    /**
     * Render tools list HTML
     */
    private function render_tools_list_html($tools, $atts) {
        $columns = max(1, min(6, intval($atts['columns'])));
        $css_classes = array('seo-tools-list', 'columns-' . $columns, 'theme-' . $atts['theme']);
        
        if (!empty($atts['class'])) {
            $css_classes[] = $atts['class'];
        }
        
        ?>
        <div class="<?php echo esc_attr(implode(' ', $css_classes)); ?>">
            <?php foreach ($tools as $tool_name): ?>
                <?php if (isset($this->tools[$tool_name])): ?>
                    <?php 
                    $tool = $this->tools[$tool_name];
                    $tool_info = $tool->get_info();
                    ?>
                    <div class="seo-tool-item">
                        <?php if ($atts['show_icons'] === 'true'): ?>
                            <div class="seo-tool-icon">
                                <i class="<?php echo esc_attr($tool_info['icon']); ?>"></i>
                            </div>
                        <?php endif; ?>
                        
                        <div class="seo-tool-content">
                            <h4 class="seo-tool-name"><?php echo esc_html($tool_info['name']); ?></h4>
                            
                            <?php if ($atts['show_descriptions'] === 'true'): ?>
                                <p class="seo-tool-desc"><?php echo esc_html($tool_info['description']); ?></p>
                            <?php endif; ?>
                            
                            <div class="seo-tool-actions">
                                <button type="button" class="seo-tool-launch-btn" data-tool="<?php echo esc_attr($tool_name); ?>">
                                    <?php _e('Use Tool', 'seo-agency-tools'); ?>
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
        <?php
    }
    
    /**
     * Check user permission
     */
    private function check_user_permission() {
        $security_settings = $this->settings->get_option('security_settings', array());
        
        // If login is not required, allow access
        if (empty($security_settings['require_login'])) {
            return true;
        }
        
        // Check if user is logged in
        if (!is_user_logged_in()) {
            return false;
        }
        
        // Check user role
        $allowed_roles = isset($security_settings['allowed_roles']) ? $security_settings['allowed_roles'] : array('administrator');
        $user = wp_get_current_user();
        
        foreach ($allowed_roles as $role) {
            if (in_array($role, $user->roles)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get available shortcodes
     */
    public function get_available_shortcodes() {
        $enabled_tools = $this->settings->get_option('enabled_tools', array());
        $shortcodes = array();
        
        // Individual tool shortcodes
        foreach ($enabled_tools as $tool_name) {
            if (isset($this->tools[$tool_name])) {
                $tool_info = $this->tools[$tool_name]->get_info();
                $shortcodes['seo_' . $tool_name] = array(
                    'name' => $tool_info['name'],
                    'description' => $tool_info['description'],
                    'example' => '[seo_' . $tool_name . ']'
                );
            }
        }
        
        // General shortcodes
        $shortcodes['seo_tool'] = array(
            'name' => __('General Tool Shortcode', 'seo-agency-tools'),
            'description' => __('Display any tool using the tool parameter', 'seo-agency-tools'),
            'example' => '[seo_tool tool="responsive_checker"]'
        );
        
        $shortcodes['seo_tools_list'] = array(
            'name' => __('Tools List', 'seo-agency-tools'),
            'description' => __('Display a list of all available tools', 'seo-agency-tools'),
            'example' => '[seo_tools_list columns="3"]'
        );
        
        return $shortcodes;
    }
}
