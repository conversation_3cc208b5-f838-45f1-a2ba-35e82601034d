<?php
/**
 * Meta Tags Validator Tool
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

require_once SEO_AGENCY_TOOLS_PLUGIN_DIR . 'tools/class-base-tool.php';

class SEO_Tools_Meta_Validator extends SEO_Tools_Base_Tool {
    
    /**
     * Initialize tool
     */
    protected function init() {
        $this->name = __('Meta Tags Validator', 'seo-agency-tools');
        $this->description = __('Validate and analyze meta tags for SEO optimization.', 'seo-agency-tools');
        $this->icon = 'dashicons-tag';
        $this->category = 'seo';
    }
    
    /**
     * Process tool request
     */
    public function process($data) {
        // Check rate limit
        if (!$this->check_rate_limit()) {
            return array(
                'success' => false,
                'error' => __('Rate limit exceeded. Please try again later.', 'seo-agency-tools')
            );
        }
        
        $url = isset($data['url']) ? sanitize_url($data['url']) : '';
        
        if (empty($url)) {
            return array(
                'success' => false,
                'error' => __('URL is required.', 'seo-agency-tools')
            );
        }
        
        $url = $this->validate_url($url);
        if (!$url) {
            return array(
                'success' => false,
                'error' => __('Invalid URL format.', 'seo-agency-tools')
            );
        }
        
        // Validate meta tags
        $results = $this->validate_meta_tags($url);
        
        // Save results
        $this->save_result($url, $results);
        
        return array(
            'success' => true,
            'data' => $results
        );
    }
    
    /**
     * Validate meta tags
     */
    private function validate_meta_tags($url) {
        $results = array(
            'url' => $url,
            'timestamp' => current_time('mysql'),
            'title' => null,
            'meta_tags' => array(),
            'seo_analysis' => array(),
            'social_media' => array(),
            'technical' => array(),
            'issues' => array(),
            'recommendations' => array(),
            'score' => 0
        );
        
        // Get page content
        $response = $this->make_request($url);
        if (!$response['success']) {
            $results['error'] = $response['error'];
            return $results;
        }
        
        $html = $response['body'];
        
        // Extract title and meta tags
        $results['title'] = $this->extract_title($html);
        $results['meta_tags'] = $this->extract_meta_tags($html);
        
        // Analyze different categories
        $results['seo_analysis'] = $this->analyze_seo_meta_tags($results);
        $results['social_media'] = $this->analyze_social_media_tags($results['meta_tags']);
        $results['technical'] = $this->analyze_technical_tags($results['meta_tags']);
        
        // Generate issues and recommendations
        $results = $this->generate_meta_recommendations($results);
        
        // Calculate score
        $results['score'] = $this->calculate_meta_score($results);
        
        return $results;
    }
    
    /**
     * Analyze SEO meta tags
     */
    private function analyze_seo_meta_tags($results) {
        $analysis = array(
            'title' => array(),
            'description' => array(),
            'keywords' => array(),
            'robots' => array(),
            'canonical' => array()
        );
        
        // Title analysis
        $title = $results['title'];
        $analysis['title'] = array(
            'present' => !empty($title),
            'content' => $title,
            'length' => strlen($title),
            'optimal_length' => strlen($title) >= 30 && strlen($title) <= 60,
            'issues' => array()
        );
        
        if (empty($title)) {
            $analysis['title']['issues'][] = __('Title tag is missing', 'seo-agency-tools');
        } elseif (strlen($title) < 30) {
            $analysis['title']['issues'][] = __('Title is too short', 'seo-agency-tools');
        } elseif (strlen($title) > 60) {
            $analysis['title']['issues'][] = __('Title may be truncated in search results', 'seo-agency-tools');
        }
        
        // Description analysis
        $description = isset($results['meta_tags']['description']) ? $results['meta_tags']['description'] : '';
        $analysis['description'] = array(
            'present' => !empty($description),
            'content' => $description,
            'length' => strlen($description),
            'optimal_length' => strlen($description) >= 120 && strlen($description) <= 160,
            'issues' => array()
        );
        
        if (empty($description)) {
            $analysis['description']['issues'][] = __('Meta description is missing', 'seo-agency-tools');
        } elseif (strlen($description) < 120) {
            $analysis['description']['issues'][] = __('Meta description is too short', 'seo-agency-tools');
        } elseif (strlen($description) > 160) {
            $analysis['description']['issues'][] = __('Meta description may be truncated', 'seo-agency-tools');
        }
        
        // Keywords analysis (deprecated but still checked)
        $keywords = isset($results['meta_tags']['keywords']) ? $results['meta_tags']['keywords'] : '';
        $analysis['keywords'] = array(
            'present' => !empty($keywords),
            'content' => $keywords,
            'deprecated' => true,
            'issues' => array()
        );
        
        if (!empty($keywords)) {
            $analysis['keywords']['issues'][] = __('Meta keywords tag is deprecated and should be removed', 'seo-agency-tools');
        }
        
        // Robots analysis
        $robots = isset($results['meta_tags']['robots']) ? $results['meta_tags']['robots'] : '';
        $analysis['robots'] = array(
            'present' => !empty($robots),
            'content' => $robots,
            'directives' => $this->parse_robots_directives($robots),
            'issues' => array()
        );
        
        if (!empty($robots)) {
            $directives = $analysis['robots']['directives'];
            if (in_array('noindex', $directives)) {
                $analysis['robots']['issues'][] = __('Page is set to noindex', 'seo-agency-tools');
            }
            if (in_array('nofollow', $directives)) {
                $analysis['robots']['issues'][] = __('Page is set to nofollow', 'seo-agency-tools');
            }
        }
        
        // Canonical analysis
        $canonical = $this->extract_canonical_url($results['meta_tags']);
        $analysis['canonical'] = array(
            'present' => !empty($canonical),
            'url' => $canonical,
            'self_referencing' => $canonical === $results['url'],
            'issues' => array()
        );
        
        if (empty($canonical)) {
            $analysis['canonical']['issues'][] = __('Canonical URL is missing', 'seo-agency-tools');
        } elseif (!$analysis['canonical']['self_referencing']) {
            $analysis['canonical']['issues'][] = __('Canonical URL points to different page', 'seo-agency-tools');
        }
        
        return $analysis;
    }
    
    /**
     * Analyze social media tags
     */
    private function analyze_social_media_tags($meta_tags) {
        $analysis = array(
            'open_graph' => array(),
            'twitter' => array(),
            'facebook' => array()
        );
        
        // Open Graph analysis
        $og_tags = $this->filter_meta_tags($meta_tags, 'og:');
        $analysis['open_graph'] = array(
            'present' => !empty($og_tags),
            'tags' => $og_tags,
            'required_tags' => array(
                'og:title' => isset($og_tags['og:title']),
                'og:description' => isset($og_tags['og:description']),
                'og:image' => isset($og_tags['og:image']),
                'og:url' => isset($og_tags['og:url']),
                'og:type' => isset($og_tags['og:type'])
            ),
            'issues' => array()
        );
        
        foreach ($analysis['open_graph']['required_tags'] as $tag => $present) {
            if (!$present) {
                $analysis['open_graph']['issues'][] = sprintf(__('Missing %s tag', 'seo-agency-tools'), $tag);
            }
        }
        
        // Twitter Card analysis
        $twitter_tags = $this->filter_meta_tags($meta_tags, 'twitter:');
        $analysis['twitter'] = array(
            'present' => !empty($twitter_tags),
            'tags' => $twitter_tags,
            'card_type' => isset($twitter_tags['twitter:card']) ? $twitter_tags['twitter:card'] : null,
            'required_tags' => array(
                'twitter:card' => isset($twitter_tags['twitter:card']),
                'twitter:title' => isset($twitter_tags['twitter:title']),
                'twitter:description' => isset($twitter_tags['twitter:description'])
            ),
            'issues' => array()
        );
        
        foreach ($analysis['twitter']['required_tags'] as $tag => $present) {
            if (!$present) {
                $analysis['twitter']['issues'][] = sprintf(__('Missing %s tag', 'seo-agency-tools'), $tag);
            }
        }
        
        // Facebook specific tags
        $fb_tags = $this->filter_meta_tags($meta_tags, 'fb:');
        $analysis['facebook'] = array(
            'present' => !empty($fb_tags),
            'tags' => $fb_tags,
            'app_id' => isset($fb_tags['fb:app_id']),
            'issues' => array()
        );
        
        return $analysis;
    }
    
    /**
     * Analyze technical tags
     */
    private function analyze_technical_tags($meta_tags) {
        $analysis = array(
            'viewport' => array(),
            'charset' => array(),
            'language' => array(),
            'author' => array(),
            'generator' => array()
        );
        
        // Viewport analysis
        $viewport = isset($meta_tags['viewport']) ? $meta_tags['viewport'] : '';
        $analysis['viewport'] = array(
            'present' => !empty($viewport),
            'content' => $viewport,
            'responsive' => strpos($viewport, 'width=device-width') !== false,
            'issues' => array()
        );
        
        if (empty($viewport)) {
            $analysis['viewport']['issues'][] = __('Viewport meta tag is missing', 'seo-agency-tools');
        } elseif (!$analysis['viewport']['responsive']) {
            $analysis['viewport']['issues'][] = __('Viewport is not configured for responsive design', 'seo-agency-tools');
        }
        
        // Charset analysis
        $charset = isset($meta_tags['charset']) ? $meta_tags['charset'] : '';
        $analysis['charset'] = array(
            'present' => !empty($charset),
            'content' => $charset,
            'utf8' => strtolower($charset) === 'utf-8',
            'issues' => array()
        );
        
        if (empty($charset)) {
            $analysis['charset']['issues'][] = __('Character encoding is not specified', 'seo-agency-tools');
        } elseif (!$analysis['charset']['utf8']) {
            $analysis['charset']['issues'][] = __('Consider using UTF-8 character encoding', 'seo-agency-tools');
        }
        
        // Language analysis
        $language = isset($meta_tags['language']) ? $meta_tags['language'] : '';
        $analysis['language'] = array(
            'present' => !empty($language),
            'content' => $language,
            'issues' => array()
        );
        
        // Author analysis
        $author = isset($meta_tags['author']) ? $meta_tags['author'] : '';
        $analysis['author'] = array(
            'present' => !empty($author),
            'content' => $author,
            'issues' => array()
        );
        
        // Generator analysis
        $generator = isset($meta_tags['generator']) ? $meta_tags['generator'] : '';
        $analysis['generator'] = array(
            'present' => !empty($generator),
            'content' => $generator,
            'security_risk' => !empty($generator),
            'issues' => array()
        );
        
        if (!empty($generator)) {
            $analysis['generator']['issues'][] = __('Generator meta tag reveals technology stack (security risk)', 'seo-agency-tools');
        }
        
        return $analysis;
    }
    
    /**
     * Parse robots directives
     */
    private function parse_robots_directives($robots) {
        if (empty($robots)) {
            return array();
        }
        
        $directives = array_map('trim', explode(',', strtolower($robots)));
        return $directives;
    }
    
    /**
     * Extract canonical URL
     */
    private function extract_canonical_url($meta_tags) {
        // Check for canonical in meta tags (though it's usually in link tags)
        foreach ($meta_tags as $name => $content) {
            if (strtolower($name) === 'canonical') {
                return $content;
            }
        }
        return null;
    }
    
    /**
     * Filter meta tags by prefix
     */
    private function filter_meta_tags($meta_tags, $prefix) {
        $filtered = array();
        foreach ($meta_tags as $name => $content) {
            if (strpos($name, $prefix) === 0) {
                $filtered[$name] = $content;
            }
        }
        return $filtered;
    }
    
    /**
     * Generate recommendations
     */
    private function generate_meta_recommendations($results) {
        $issues = array();
        $recommendations = array();
        
        // SEO recommendations
        foreach ($results['seo_analysis'] as $category => $analysis) {
            if (isset($analysis['issues']) && !empty($analysis['issues'])) {
                foreach ($analysis['issues'] as $issue) {
                    $issues[] = array(
                        'category' => 'seo',
                        'type' => $category,
                        'message' => $issue,
                        'priority' => $this->get_issue_priority($category, $issue)
                    );
                }
            }
        }
        
        // Social media recommendations
        foreach ($results['social_media'] as $platform => $analysis) {
            if (isset($analysis['issues']) && !empty($analysis['issues'])) {
                foreach ($analysis['issues'] as $issue) {
                    $issues[] = array(
                        'category' => 'social',
                        'type' => $platform,
                        'message' => $issue,
                        'priority' => 'medium'
                    );
                }
            }
        }
        
        // Technical recommendations
        foreach ($results['technical'] as $category => $analysis) {
            if (isset($analysis['issues']) && !empty($analysis['issues'])) {
                foreach ($analysis['issues'] as $issue) {
                    $issues[] = array(
                        'category' => 'technical',
                        'type' => $category,
                        'message' => $issue,
                        'priority' => $this->get_technical_priority($category)
                    );
                }
            }
        }
        
        // Generate specific recommendations
        $recommendations = $this->generate_specific_recommendations($results);
        
        $results['issues'] = $issues;
        $results['recommendations'] = $recommendations;
        
        return $results;
    }
    
    /**
     * Get issue priority
     */
    private function get_issue_priority($category, $issue) {
        $high_priority = array('title', 'description');
        $medium_priority = array('canonical', 'robots');
        
        if (in_array($category, $high_priority)) {
            return 'high';
        } elseif (in_array($category, $medium_priority)) {
            return 'medium';
        }
        
        return 'low';
    }
    
    /**
     * Get technical priority
     */
    private function get_technical_priority($category) {
        $priorities = array(
            'viewport' => 'high',
            'charset' => 'high',
            'language' => 'low',
            'author' => 'low',
            'generator' => 'medium'
        );
        
        return isset($priorities[$category]) ? $priorities[$category] : 'low';
    }
    
    /**
     * Generate specific recommendations
     */
    private function generate_specific_recommendations($results) {
        $recommendations = array();
        
        // Title recommendations
        if (empty($results['title'])) {
            $recommendations[] = array(
                'type' => 'title',
                'priority' => 'high',
                'message' => __('Add a descriptive title tag', 'seo-agency-tools'),
                'example' => '<title>Your Page Title - Brand Name</title>'
            );
        }
        
        // Description recommendations
        if (empty($results['meta_tags']['description'])) {
            $recommendations[] = array(
                'type' => 'description',
                'priority' => 'high',
                'message' => __('Add a meta description', 'seo-agency-tools'),
                'example' => '<meta name="description" content="Your page description here">'
            );
        }
        
        // Viewport recommendations
        if (empty($results['meta_tags']['viewport'])) {
            $recommendations[] = array(
                'type' => 'viewport',
                'priority' => 'high',
                'message' => __('Add viewport meta tag for mobile responsiveness', 'seo-agency-tools'),
                'example' => '<meta name="viewport" content="width=device-width, initial-scale=1">'
            );
        }
        
        return $recommendations;
    }
    
    /**
     * Calculate meta score
     */
    private function calculate_meta_score($results) {
        $score = 0;
        $max_score = 100;
        
        // SEO score (60 points)
        $seo_score = 0;
        if (!empty($results['title'])) $seo_score += 20;
        if (!empty($results['meta_tags']['description'])) $seo_score += 20;
        if ($results['seo_analysis']['title']['optimal_length']) $seo_score += 10;
        if ($results['seo_analysis']['description']['optimal_length']) $seo_score += 10;
        
        $score += $seo_score;
        
        // Technical score (25 points)
        $technical_score = 0;
        if ($results['technical']['viewport']['present']) $technical_score += 10;
        if ($results['technical']['charset']['present']) $technical_score += 10;
        if (!$results['technical']['generator']['present']) $technical_score += 5; // Bonus for not revealing generator
        
        $score += $technical_score;
        
        // Social media score (15 points)
        $social_score = 0;
        if ($results['social_media']['open_graph']['present']) $social_score += 10;
        if ($results['social_media']['twitter']['present']) $social_score += 5;
        
        $score += $social_score;
        
        return min($score, $max_score);
    }
    
    /**
     * Render tool form
     */
    public function render_form($atts = array()) {
        $atts['placeholder'] = __('Enter URL to validate meta tags...', 'seo-agency-tools');
        $atts['button_text'] = __('Validate Meta Tags', 'seo-agency-tools');
        
        return $this->render_basic_form($atts);
    }
}
