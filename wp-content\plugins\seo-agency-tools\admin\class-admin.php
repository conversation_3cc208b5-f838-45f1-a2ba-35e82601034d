<?php
/**
 * Admin interface class for SEO Agency Tools
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SEO_Tools_Admin {
    
    /**
     * Settings instance
     */
    private $settings;
    
    /**
     * Database instance
     */
    private $database;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->settings = new SEO_Tools_Settings();
        $this->database = new SEO_Tools_Database();
        
        add_action('admin_init', array($this, 'handle_admin_actions'));
    }
    
    /**
     * Render main admin page
     */
    public function render_main_page() {
        $active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'dashboard';
        
        ?>
        <div class="wrap">
            <h1><?php _e('SEO Agency Tools', 'seo-agency-tools'); ?></h1>
            
            <nav class="nav-tab-wrapper">
                <a href="?page=seo-agency-tools&tab=dashboard" 
                   class="nav-tab <?php echo $active_tab === 'dashboard' ? 'nav-tab-active' : ''; ?>">
                    <?php _e('Dashboard', 'seo-agency-tools'); ?>
                </a>
                <a href="?page=seo-agency-tools&tab=tools" 
                   class="nav-tab <?php echo $active_tab === 'tools' ? 'nav-tab-active' : ''; ?>">
                    <?php _e('Tools', 'seo-agency-tools'); ?>
                </a>
                <a href="?page=seo-agency-tools&tab=results" 
                   class="nav-tab <?php echo $active_tab === 'results' ? 'nav-tab-active' : ''; ?>">
                    <?php _e('Results', 'seo-agency-tools'); ?>
                </a>
                <a href="?page=seo-agency-tools&tab=shortcodes" 
                   class="nav-tab <?php echo $active_tab === 'shortcodes' ? 'nav-tab-active' : ''; ?>">
                    <?php _e('Shortcodes', 'seo-agency-tools'); ?>
                </a>
            </nav>
            
            <div class="tab-content">
                <?php
                switch ($active_tab) {
                    case 'dashboard':
                        $this->render_dashboard_tab();
                        break;
                    case 'tools':
                        $this->render_tools_tab();
                        break;
                    case 'results':
                        $this->render_results_tab();
                        break;
                    case 'shortcodes':
                        $this->render_shortcodes_tab();
                        break;
                    default:
                        $this->render_dashboard_tab();
                }
                ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render settings page
     */
    public function render_settings_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('SEO Tools Settings', 'seo-agency-tools'); ?></h1>
            
            <form method="post" action="options.php">
                <?php
                settings_fields('seo_agency_tools_settings');
                do_settings_sections('seo_agency_tools_settings');
                submit_button();
                ?>
            </form>
        </div>
        <?php
    }
    
    /**
     * Render dashboard tab
     */
    private function render_dashboard_tab() {
        $stats = $this->database->get_statistics();
        $enabled_tools = $this->settings->get_option('enabled_tools', array());
        
        ?>
        <div class="dashboard-widgets">
            <div class="dashboard-widget-row">
                <div class="dashboard-widget">
                    <h3><?php _e('Statistics', 'seo-agency-tools'); ?></h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-number"><?php echo number_format($stats['total_results']); ?></span>
                            <span class="stat-label"><?php _e('Total Analyses', 'seo-agency-tools'); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?php echo count($enabled_tools); ?></span>
                            <span class="stat-label"><?php _e('Active Tools', 'seo-agency-tools'); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?php echo number_format($stats['recent_activity']); ?></span>
                            <span class="stat-label"><?php _e('This Week', 'seo-agency-tools'); ?></span>
                        </div>
                    </div>
                </div>
                
                <div class="dashboard-widget">
                    <h3><?php _e('Popular Tools', 'seo-agency-tools'); ?></h3>
                    <div class="popular-tools">
                        <?php if (!empty($stats['by_tool'])): ?>
                            <?php foreach (array_slice($stats['by_tool'], 0, 5) as $tool => $count): ?>
                                <div class="tool-stat">
                                    <span class="tool-name"><?php echo esc_html(ucwords(str_replace('_', ' ', $tool))); ?></span>
                                    <span class="tool-count"><?php echo number_format($count); ?></span>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p><?php _e('No data available yet.', 'seo-agency-tools'); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-widget-row">
                <div class="dashboard-widget">
                    <h3><?php _e('Quick Actions', 'seo-agency-tools'); ?></h3>
                    <div class="quick-actions">
                        <a href="?page=seo-agency-tools-settings" class="button button-primary">
                            <?php _e('Configure Settings', 'seo-agency-tools'); ?>
                        </a>
                        <a href="?page=seo-agency-tools&tab=shortcodes" class="button">
                            <?php _e('View Shortcodes', 'seo-agency-tools'); ?>
                        </a>
                        <a href="?page=seo-agency-tools&tab=results" class="button">
                            <?php _e('View Results', 'seo-agency-tools'); ?>
                        </a>
                        <button type="button" class="button" onclick="seoToolsCleanOldResults()">
                            <?php _e('Clean Old Results', 'seo-agency-tools'); ?>
                        </button>
                    </div>
                </div>
                
                <div class="dashboard-widget">
                    <h3><?php _e('System Status', 'seo-agency-tools'); ?></h3>
                    <div class="system-status">
                        <?php $this->render_system_status(); ?>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render tools tab
     */
    private function render_tools_tab() {
        $tools = SEO_Agency_Tools::get_instance()->get_tools();
        $enabled_tools = $this->settings->get_option('enabled_tools', array());
        
        ?>
        <div class="tools-management">
            <h2><?php _e('Available Tools', 'seo-agency-tools'); ?></h2>
            <p><?php _e('Manage your SEO tools. You can enable/disable tools and configure their settings.', 'seo-agency-tools'); ?></p>
            
            <div class="tools-grid">
                <?php foreach ($tools as $tool_key => $tool_instance): ?>
                    <?php 
                    $tool_info = $tool_instance->get_info();
                    $is_enabled = in_array($tool_key, $enabled_tools);
                    ?>
                    <div class="tool-card <?php echo $is_enabled ? 'enabled' : 'disabled'; ?>">
                        <div class="tool-header">
                            <div class="tool-icon">
                                <i class="<?php echo esc_attr($tool_info['icon']); ?>"></i>
                            </div>
                            <div class="tool-title">
                                <h3><?php echo esc_html($tool_info['name']); ?></h3>
                                <span class="tool-status">
                                    <?php echo $is_enabled ? __('Enabled', 'seo-agency-tools') : __('Disabled', 'seo-agency-tools'); ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="tool-description">
                            <p><?php echo esc_html($tool_info['description']); ?></p>
                        </div>
                        
                        <div class="tool-actions">
                            <button type="button" class="button tool-toggle-btn" 
                                    data-tool="<?php echo esc_attr($tool_key); ?>"
                                    data-enabled="<?php echo $is_enabled ? '1' : '0'; ?>">
                                <?php echo $is_enabled ? __('Disable', 'seo-agency-tools') : __('Enable', 'seo-agency-tools'); ?>
                            </button>
                            
                            <?php if ($is_enabled): ?>
                                <button type="button" class="button tool-test-btn" 
                                        data-tool="<?php echo esc_attr($tool_key); ?>">
                                    <?php _e('Test', 'seo-agency-tools'); ?>
                                </button>
                                <button type="button" class="button tool-settings-btn" 
                                        data-tool="<?php echo esc_attr($tool_key); ?>">
                                    <?php _e('Settings', 'seo-agency-tools'); ?>
                                </button>
                            <?php endif; ?>
                        </div>
                        
                        <div class="tool-shortcode">
                            <strong><?php _e('Shortcode:', 'seo-agency-tools'); ?></strong>
                            <code>[seo_<?php echo esc_html($tool_key); ?>]</code>
                            <button type="button" class="copy-shortcode-btn" 
                                    data-shortcode="[seo_<?php echo esc_attr($tool_key); ?>]">
                                <?php _e('Copy', 'seo-agency-tools'); ?>
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render results tab
     */
    private function render_results_tab() {
        $page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $per_page = 20;
        $offset = ($page - 1) * $per_page;
        
        $tool_filter = isset($_GET['tool_filter']) ? sanitize_text_field($_GET['tool_filter']) : '';
        
        $results = $this->database->get_results($tool_filter, $per_page, $offset);
        $total_results = $this->database->get_results_count($tool_filter);
        $total_pages = ceil($total_results / $per_page);
        
        ?>
        <div class="results-management">
            <div class="results-header">
                <h2><?php _e('Analysis Results', 'seo-agency-tools'); ?></h2>
                
                <div class="results-filters">
                    <form method="get">
                        <input type="hidden" name="page" value="seo-agency-tools">
                        <input type="hidden" name="tab" value="results">
                        
                        <select name="tool_filter">
                            <option value=""><?php _e('All Tools', 'seo-agency-tools'); ?></option>
                            <?php
                            $tools = SEO_Agency_Tools::get_instance()->get_tools();
                            foreach ($tools as $tool_key => $tool_instance):
                                $tool_info = $tool_instance->get_info();
                                $selected = selected($tool_filter, $tool_key, false);
                            ?>
                                <option value="<?php echo esc_attr($tool_key); ?>" <?php echo $selected; ?>>
                                    <?php echo esc_html($tool_info['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        
                        <input type="submit" class="button" value="<?php _e('Filter', 'seo-agency-tools'); ?>">
                    </form>
                </div>
            </div>
            
            <?php if (!empty($results)): ?>
                <div class="results-table-wrapper">
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php _e('Tool', 'seo-agency-tools'); ?></th>
                                <th><?php _e('URL', 'seo-agency-tools'); ?></th>
                                <th><?php _e('Status', 'seo-agency-tools'); ?></th>
                                <th><?php _e('Date', 'seo-agency-tools'); ?></th>
                                <th><?php _e('Actions', 'seo-agency-tools'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($results as $result): ?>
                                <tr>
                                    <td><?php echo esc_html(ucwords(str_replace('_', ' ', $result->tool_name))); ?></td>
                                    <td>
                                        <a href="<?php echo esc_url($result->url); ?>" target="_blank">
                                            <?php echo esc_html($result->url); ?>
                                        </a>
                                    </td>
                                    <td>
                                        <span class="status-badge status-<?php echo esc_attr($result->status); ?>">
                                            <?php echo esc_html(ucfirst($result->status)); ?>
                                        </span>
                                    </td>
                                    <td><?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($result->created_at))); ?></td>
                                    <td>
                                        <button type="button" class="button view-result-btn" 
                                                data-result-id="<?php echo esc_attr($result->id); ?>">
                                            <?php _e('View', 'seo-agency-tools'); ?>
                                        </button>
                                        <button type="button" class="button delete-result-btn" 
                                                data-result-id="<?php echo esc_attr($result->id); ?>">
                                            <?php _e('Delete', 'seo-agency-tools'); ?>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <?php if ($total_pages > 1): ?>
                    <div class="tablenav">
                        <div class="tablenav-pages">
                            <?php
                            $pagination_args = array(
                                'base' => add_query_arg('paged', '%#%'),
                                'format' => '',
                                'prev_text' => __('&laquo;'),
                                'next_text' => __('&raquo;'),
                                'total' => $total_pages,
                                'current' => $page
                            );
                            echo paginate_links($pagination_args);
                            ?>
                        </div>
                    </div>
                <?php endif; ?>
                
            <?php else: ?>
                <div class="no-results">
                    <p><?php _e('No results found.', 'seo-agency-tools'); ?></p>
                </div>
            <?php endif; ?>
        </div>
        <?php
    }
    
    /**
     * Render shortcodes tab
     */
    private function render_shortcodes_tab() {
        $shortcodes_instance = new SEO_Tools_Shortcodes(SEO_Agency_Tools::get_instance()->get_tools());
        $shortcodes = $shortcodes_instance->get_available_shortcodes();
        
        ?>
        <div class="shortcodes-reference">
            <h2><?php _e('Available Shortcodes', 'seo-agency-tools'); ?></h2>
            <p><?php _e('Use these shortcodes to display SEO tools on your pages and posts.', 'seo-agency-tools'); ?></p>
            
            <div class="shortcodes-grid">
                <?php foreach ($shortcodes as $shortcode => $info): ?>
                    <div class="shortcode-card">
                        <h3><?php echo esc_html($info['name']); ?></h3>
                        <p><?php echo esc_html($info['description']); ?></p>
                        
                        <div class="shortcode-example">
                            <strong><?php _e('Example:', 'seo-agency-tools'); ?></strong>
                            <code><?php echo esc_html($info['example']); ?></code>
                            <button type="button" class="copy-shortcode-btn" 
                                    data-shortcode="<?php echo esc_attr($info['example']); ?>">
                                <?php _e('Copy', 'seo-agency-tools'); ?>
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <div class="shortcode-attributes">
                <h3><?php _e('Common Attributes', 'seo-agency-tools'); ?></h3>
                <table class="wp-list-table widefat">
                    <thead>
                        <tr>
                            <th><?php _e('Attribute', 'seo-agency-tools'); ?></th>
                            <th><?php _e('Description', 'seo-agency-tools'); ?></th>
                            <th><?php _e('Default', 'seo-agency-tools'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><code>title</code></td>
                            <td><?php _e('Custom title for the tool', 'seo-agency-tools'); ?></td>
                            <td><?php _e('Tool default title', 'seo-agency-tools'); ?></td>
                        </tr>
                        <tr>
                            <td><code>description</code></td>
                            <td><?php _e('Custom description for the tool', 'seo-agency-tools'); ?></td>
                            <td><?php _e('Tool default description', 'seo-agency-tools'); ?></td>
                        </tr>
                        <tr>
                            <td><code>theme</code></td>
                            <td><?php _e('Visual theme (default, dark, light, minimal)', 'seo-agency-tools'); ?></td>
                            <td>default</td>
                        </tr>
                        <tr>
                            <td><code>width</code></td>
                            <td><?php _e('Tool container width', 'seo-agency-tools'); ?></td>
                            <td>100%</td>
                        </tr>
                        <tr>
                            <td><code>class</code></td>
                            <td><?php _e('Additional CSS classes', 'seo-agency-tools'); ?></td>
                            <td><?php _e('None', 'seo-agency-tools'); ?></td>
                        </tr>
                        <tr>
                            <td><code>button_text</code></td>
                            <td><?php _e('Custom text for the submit button', 'seo-agency-tools'); ?></td>
                            <td><?php _e('Analyze', 'seo-agency-tools'); ?></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render system status
     */
    private function render_system_status() {
        $checks = array(
            'php_version' => array(
                'label' => __('PHP Version', 'seo-agency-tools'),
                'value' => PHP_VERSION,
                'status' => version_compare(PHP_VERSION, '7.4', '>=') ? 'good' : 'warning'
            ),
            'curl_enabled' => array(
                'label' => __('cURL Extension', 'seo-agency-tools'),
                'value' => function_exists('curl_init') ? __('Enabled', 'seo-agency-tools') : __('Disabled', 'seo-agency-tools'),
                'status' => function_exists('curl_init') ? 'good' : 'error'
            ),
            'memory_limit' => array(
                'label' => __('Memory Limit', 'seo-agency-tools'),
                'value' => ini_get('memory_limit'),
                'status' => 'good'
            ),
            'max_execution_time' => array(
                'label' => __('Max Execution Time', 'seo-agency-tools'),
                'value' => ini_get('max_execution_time') . 's',
                'status' => 'good'
            )
        );
        
        foreach ($checks as $check) {
            echo '<div class="status-item status-' . esc_attr($check['status']) . '">';
            echo '<span class="status-label">' . esc_html($check['label']) . ':</span>';
            echo '<span class="status-value">' . esc_html($check['value']) . '</span>';
            echo '</div>';
        }
    }
    
    /**
     * Handle admin actions
     */
    public function handle_admin_actions() {
        if (!current_user_can('manage_options')) {
            return;
        }
        
        // Handle AJAX actions
        add_action('wp_ajax_seo_tools_toggle_tool', array($this, 'ajax_toggle_tool'));
        add_action('wp_ajax_seo_tools_delete_result', array($this, 'ajax_delete_result'));
        add_action('wp_ajax_seo_tools_clean_old_results', array($this, 'ajax_clean_old_results'));
    }
    
    /**
     * AJAX: Toggle tool enabled/disabled
     */
    public function ajax_toggle_tool() {
        check_ajax_referer('seo_tools_admin_nonce', 'nonce');
        
        $tool = sanitize_text_field($_POST['tool']);
        $enabled = intval($_POST['enabled']);
        
        $options = $this->settings->get_options();
        $enabled_tools = $options['enabled_tools'];
        
        if ($enabled) {
            if (!in_array($tool, $enabled_tools)) {
                $enabled_tools[] = $tool;
            }
        } else {
            $enabled_tools = array_diff($enabled_tools, array($tool));
        }
        
        $options['enabled_tools'] = $enabled_tools;
        update_option('seo_agency_tools_options', $options);
        
        wp_send_json_success(array(
            'message' => $enabled ? __('Tool enabled', 'seo-agency-tools') : __('Tool disabled', 'seo-agency-tools')
        ));
    }
    
    /**
     * AJAX: Delete result
     */
    public function ajax_delete_result() {
        check_ajax_referer('seo_tools_admin_nonce', 'nonce');
        
        $result_id = intval($_POST['result_id']);
        
        if ($this->database->delete_result($result_id)) {
            wp_send_json_success(array(
                'message' => __('Result deleted', 'seo-agency-tools')
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to delete result', 'seo-agency-tools')
            ));
        }
    }
    
    /**
     * AJAX: Clean old results
     */
    public function ajax_clean_old_results() {
        check_ajax_referer('seo_tools_admin_nonce', 'nonce');
        
        $deleted = $this->database->clean_old_results(30);
        
        wp_send_json_success(array(
            'message' => sprintf(__('Deleted %d old results', 'seo-agency-tools'), $deleted)
        ));
    }
}
