# SEO Agency Tools WordPress Plugin

A comprehensive collection of SEO and website analysis tools for agencies and professionals. This plugin provides 15 powerful tools that can be easily embedded on any page using shortcodes and customized through the admin interface.

## Features

### 🛠️ Available Tools

1. **Responsive Design Checker** - Check how websites look on different screen sizes
2. **Page Title Enhancer** - Analyze and optimize page titles for better SEO
3. **Rich Snippet Validator** - Validate structured data and schema markup
4. **Anchor Text Generator** - Generate SEO-friendly anchor text
5. **Lazy Loading Tester** - Test image lazy loading implementation
6. **Bulk Social Media Meta Tags Validator** - Validate multiple URLs for social media tags
7. **Social Media Meta Tags Validator** - Validate Open Graph and Twitter Card tags
8. **Bulk Sitemap Finder** - Find sitemaps for multiple websites
9. **Bulk MX Record Lookup** - Check email server configurations
10. **Bulk HTTP/2 Check** - Verify HTTP/2 support across multiple URLs
11. **Bulk Cache-Control Header Checker** - Analyze caching headers
12. **Bulk Subdomain Finder** - Discover subdomains for websites
13. **Bulk TTFB Checker** - Measure Time To First Byte performance
14. **HTTP Header Checker** - Comprehensive HTTP header analysis
15. **Bulk Redirect Checker** - Check redirect chains and status codes

### 🎨 Customization Features

- **Custom CSS** - Add your own styles to match your website design
- **Custom JavaScript** - Enhance functionality with custom scripts
- **Multiple Themes** - Choose from default, dark, light, and minimal themes
- **Color Schemes** - Select from blue, green, red, purple, and orange color schemes
- **Responsive Design** - All tools work perfectly on mobile devices

### 🔧 Admin Features

- **Tool Management** - Enable/disable individual tools
- **Results Dashboard** - View and manage analysis results
- **Statistics** - Track tool usage and performance
- **Shortcode Generator** - Easy shortcode creation and copying
- **Settings Panel** - Comprehensive configuration options
- **Security Controls** - User access management and rate limiting

## Installation

1. Upload the `seo-agency-tools` folder to your `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Go to 'SEO Tools' in your admin menu to configure the plugin

## Usage

### Basic Shortcodes

Display individual tools using their specific shortcodes:

```
[seo_responsive_checker]
[seo_title_enhancer]
[seo_header_checker]
[seo_meta_validator]
```

### General Shortcode

Use the general shortcode to display any tool:

```
[seo_tool tool="responsive_checker"]
[seo_tool tool="title_enhancer" title="Custom Title"]
```

### Tools List

Display a list of all available tools:

```
[seo_tools_list]
[seo_tools_list columns="3" theme="grid"]
```

### Shortcode Attributes

All shortcodes support these common attributes:

- `title` - Custom title for the tool
- `description` - Custom description
- `theme` - Visual theme (default, dark, light, minimal)
- `width` - Tool container width
- `height` - Tool container height
- `class` - Additional CSS classes
- `button_text` - Custom submit button text
- `show_title` - Show/hide title (true/false)
- `show_description` - Show/hide description (true/false)
- `show_results` - Show/hide results section (true/false)

### Examples

```html
<!-- Responsive checker with custom styling -->
[seo_responsive_checker title="Check Mobile Compatibility" theme="dark" width="100%"]

<!-- Title enhancer with custom button text -->
[seo_title_enhancer button_text="Optimize My Title" class="my-custom-class"]

<!-- Tools list in 2 columns -->
[seo_tools_list columns="2" show_descriptions="true"]
```

## Configuration

### Plugin Settings

Access settings via **SEO Tools > Settings**:

1. **Tool Management** - Enable/disable specific tools
2. **Customization** - Add custom CSS and JavaScript
3. **API Settings** - Configure rate limits and timeouts
4. **Security** - Set user access controls
5. **Display** - Choose themes and color schemes

### Custom CSS Example

```css
.seo-tool-container {
    border: 2px solid #your-color;
    border-radius: 10px;
}

.seo-tool-submit-btn {
    background: #your-brand-color;
}
```

### Custom JavaScript Example

```javascript
jQuery(document).ready(function($) {
    // Custom functionality
    $('.seo-tool-container').on('tool-complete', function(e, data) {
        console.log('Tool completed:', data);
    });
});
```

## Database Tables

The plugin creates two database tables:

- `wp_seo_tool_results` - Stores analysis results
- `wp_seo_tool_settings` - Stores tool-specific settings

## API Endpoints

The plugin uses WordPress AJAX for tool processing:

- `wp_ajax_seo_tools_run` - Execute tool analysis
- `wp_ajax_seo_tools_delete_result` - Delete analysis result
- `wp_ajax_seo_tools_toggle_tool` - Enable/disable tools

## Security Features

- **Rate Limiting** - Prevents abuse with configurable limits
- **Nonce Verification** - CSRF protection for all requests
- **User Permissions** - Role-based access control
- **Input Sanitization** - All inputs are properly sanitized
- **SQL Injection Protection** - Prepared statements used throughout

## Performance Considerations

- **Caching** - Results are cached to improve performance
- **Async Processing** - Tools run asynchronously to prevent timeouts
- **Resource Optimization** - CSS and JS are minified and combined
- **Database Cleanup** - Automatic cleanup of old results

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Internet Explorer 11 (limited support)

## Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher
- MySQL 5.6 or higher
- cURL extension enabled
- 128MB memory limit recommended

## Troubleshooting

### Common Issues

1. **Tools not loading** - Check if JavaScript is enabled and no console errors
2. **Rate limit exceeded** - Increase rate limit in settings or wait
3. **Timeout errors** - Increase timeout settings or check server resources
4. **Permission denied** - Verify user roles in security settings

### Debug Mode

Enable WordPress debug mode to see detailed error messages:

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Hooks and Filters

### Actions

```php
// Before tool execution
do_action('seo_tools_before_process', $tool_name, $data);

// After tool execution
do_action('seo_tools_after_process', $tool_name, $results);

// Before saving results
do_action('seo_tools_before_save_result', $tool_name, $url, $data);
```

### Filters

```php
// Modify tool results
$results = apply_filters('seo_tools_process_results', $results, $tool_name);

// Customize tool settings
$settings = apply_filters('seo_tools_settings', $settings, $tool_name);

// Modify shortcode output
$output = apply_filters('seo_tools_shortcode_output', $output, $atts, $tool_name);
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This plugin is licensed under the GPL v2 or later.

## Support

For support and questions:
- Check the documentation
- Review common issues in troubleshooting
- Contact plugin support

## Changelog

### Version 1.0.0
- Initial release
- 15 SEO tools included
- Full admin interface
- Shortcode system
- Customization options
- Security features
